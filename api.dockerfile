
ARG BASEDIR=.

# Use the official lightweight Node.js 18 image.
# https://hub.docker.com/_/node
FROM node:22-alpine

# Create and change to the app directory.
WORKDIR /usr/src/app

# TODO: verify if it's necessary to compile native modules
RUN apk add --no-cache python3 make g++

# Copy application dependency manifests to the container image.
COPY player-lynk/back/commons/package.json player-lynk/back/commons/yarn.lock ./back/commons/
COPY player-lynk/back/commons/.npmrc.prod ./back/commons/.npmrc
COPY player-lynk/back/api/package.json player-lynk/back/api/yarn.lock ./back/api/
COPY player-lynk/back/api/.npmrc.prod ./back/api/.npmrc

# init commons
WORKDIR /usr/src/app/back/commons
RUN yarn install --frozen-lockfile

## init api
WORKDIR /usr/src/app/back/api
RUN yarn install --frozen-lockfile

## copy the project
WORKDIR /usr/src/app
COPY player-lynk/ ./

ENV NODE_ENV=production

## Build API
WORKDIR /usr/src/app/back/api
RUN yarn build

## Run the web service on container startup.
EXPOSE 3000
CMD ["node", "dist/back/api/src/server.js"]
