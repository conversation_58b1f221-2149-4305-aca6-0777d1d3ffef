import vue from '@vitejs/plugin-vue';
import { URL, fileURLToPath } from 'node:url';
import { type UserConfig, defineConfig, loadEnv } from 'vite';
import svgLoader from 'vite-svg-loader';

// https://vitejs.dev/config/

export default defineConfig(({ mode }): UserConfig => {
  const env = loadEnv(mode, process.cwd(), '');
  console.log('VITE API URL: ', env.VITE_API_URL);
  return {
    server: {
      port: parseInt(env.VITE_PORT),
    },
    plugins: [vue(), svgLoader()],
    resolve: {
      alias: {
        // @ts-ignore
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        // @ts-ignore
        '@shared': fileURLToPath(new URL('../shared', import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler', // or "modern"
          additionalData: `@use "sass:math";@use "@/scss/variables" as *;@use "@/scss/mixins" as *;`,
        },
      },
    },
  };
});
