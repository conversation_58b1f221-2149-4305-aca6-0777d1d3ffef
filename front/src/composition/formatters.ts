import type { DataSet, IPlayer, IUserDB, Person, price } from '@shared/types';
import { useI18n } from 'vue-i18n';

import type { SelectOption } from '@/front-types';

export type PriceOptions = {
	notCents?: boolean;
	thousandSeparator?: string;
	undisplayCents?: boolean;
};

export type UseFormattersResult = {
	formatPrice: (price: number | null | undefined, suffix?: string | false, options?: PriceOptions) => string;
	decodePrice: (value: string) => price | null;
	paragraphize: (text?: string | null) => string | null;
	getSelectOptions: (e: { [key: string]: string }, name: string) => SelectOption<string>[];
	formatHours: (minutes: number) => string;
	capitalize: (text: string | null) => string | null;
	keywordsToString: (keywords?: string[] | null) => string;
	stringToKeywords: (str?: string | null) => string[];
	formatStorageSize: (bytes?: number | null, defaultValue?: string | null) => string | null;
	getImageExtension: (mimetype: string) => string;
	fullname: (person?: Person | null) => string;
	playerFullname: (player?: IPlayer | IUserDB | null) => string;
	getPlayerPosition: (player?: IPlayer | null) => string;
};

export function useFormatters(): UseFormattersResult {
	return {
		formatPrice,
		decodePrice,
		paragraphize,
		getSelectOptions,
		formatHours,
		capitalize,
		keywordsToString,
		stringToKeywords,
		formatStorageSize,
		getImageExtension,
		fullname,
		playerFullname,
		getPlayerPosition,
	};
}

function formatPrice(price: number | null | undefined, suffix?: string | false, options?: PriceOptions): string {
	if (price === null || price === undefined) {
		return '';
	}
	let negative = Math.sign(price) === -1;
	price = options && options.notCents ? Math.abs(Math.round(price * 100)) : Math.abs(Math.round(price));
	let int = Math.floor(price / 100);
	let intStr = '' + int;
	if (options === undefined || options === null || options.thousandSeparator !== '') {
		let separator =
			!options || options.thousandSeparator === null || options.thousandSeparator === undefined
				? '&nbsp;'
				: options.thousandSeparator;
		let formattedInt = '';
		let part;
		for (let i = intStr.length; i > 0; i -= 3) {
			if (i >= 3) {
				part = intStr.substring(i - 3, i);
			} else if (i > 0) {
				part = intStr.substring(0, i);
			} else {
				part = null;
			}

			if (part !== null) {
				if (formattedInt.length > 0) {
					formattedInt = part + separator + formattedInt;
				} else {
					formattedInt = part;
				}
			}
		}
		intStr = formattedInt;
	}
	let frac = price % 100;
	let fracStr;
	let absFrac = Math.abs(frac);
	if (absFrac < 10) {
		fracStr = '0' + absFrac;
	} else {
		fracStr = absFrac.toString();
	}

	if (isNaN(int) || isNaN(parseInt(fracStr))) {
		return '-';
	} else {
		let suffixStr;
		if (suffix) {
			suffixStr = suffix;
		} else {
			if (suffix === false) {
				suffixStr = '';
			} else {
				suffixStr = '&nbsp;€';
			}
		}
		return (negative ? '-' : '') + intStr + (options && options.undisplayCents ? '' : ',' + fracStr) + suffixStr;
	}
}

function decodePrice(value: string): price | null {
	const regex = /^(\d+)([,.](\d+))?.*$/;
	if (value) {
		const match = value.trim().match(regex);
		console.log('match...', match);
		if (match) {
			const int = match[1];
			const frac = match[3];
			if (match[1] !== undefined) {
				let price = parseInt(int) * 100;
				if (frac) {
					let fracInt = parseInt(frac.substring(0, 2));
					if (fracInt < 10) {
						fracInt *= 10;
					}
					price += fracInt;
				}
				return price;
			}
		}
	}
	return null;
}
function paragraphize(text?: string | null): string | null {
	if (text) {
		return text
			.split('\n')
			.map((p: string) => {
				return '<p>' + p + '</p>';
			})
			.join('');
	} else {
		return null;
	}
}

function getSelectOptions(e: { [key: string]: string }, name: string): SelectOption<string>[] {
	const { t } = useI18n();

	return Object.keys(e).map((e) => {
		return {
			value: e,
			label: t('enum.' + name + '.' + e),
		};
	});
}

function formatHours(minutes: number): string {
	const { t } = useI18n();

	if (minutes < 0 || minutes === undefined || minutes === null) {
		minutes = 0;
	}

	const hours = Math.floor(minutes / 60);
	const mins = Math.floor(minutes) % 60;
	const minStr = mins > 0 ? mins.toString().padStart(2, '0') : '';
	return t('global.durationFormat', [hours, minStr]);
}

function capitalize(text: string | null): string | null {
	if (text) {
		if (text.length > 0) {
			if (text.length > 1) {
				return text.substring(0, 1).toUpperCase() + text.substring(1);
			} else {
				return text.toUpperCase();
			}
		}
	}

	return text;
}

function keywordsToString(keywords?: string[] | null): string {
	if (keywords) {
		return keywords.join(', ');
	} else {
		return '';
	}
}

function stringToKeywords(str?: string | null): string[] {
	let keywords: DataSet<string> = {};
	if (str) {
		let lines = str.split('\n');
		for (const line of lines) {
			let kws = line.split(',');
			for (const kw of kws) {
				let k = kw.trim().toLowerCase();
				if (k.length > 0) {
					keywords[k] = k;
				}
			}
		}
	}

	console.log('string to keyword: ' + str + ' => ', keywords);
	return Object.keys(keywords);
}

function formatStorageSize(bytes?: number | null, defaultValue?: string | null): string | null {
	if (!!bytes && bytes > 0) {
		let suffix: string;
		let value = bytes / 1024;
		if (value > 1000) {
			value /= 1024;
			if (value > 1000) {
				value /= 1024;
				if (value > 1000) {
					value /= 1024;
					suffix = 'To';
				} else {
					suffix = 'Go';
				}
			} else {
				suffix = 'Mo';
			}
		} else {
			suffix = 'Ko';
		}
		return value.toFixed(1).replace(/\./g, ',') + ' ' + suffix;
	} else {
		if (defaultValue) {
			return defaultValue;
		} else {
			return '0';
		}
	}
}

function getImageExtension(mimetype: string): string {
	switch (mimetype) {
		case 'image/jpeg':
		case 'image/jpg':
			return 'jpg';
		case 'image/png':
			return 'png';
		case 'image/gif':
			return 'gif';
		default: {
			const idx = mimetype.lastIndexOf('/');
			if (idx >= 0) {
				return mimetype.substring(idx + 1);
			} else {
				return mimetype;
			}
		}
	}
}

function fullname(person?: Person | null): string {
	if (person) {
		let str = '';
		if (person.firstname) {
			str += capitalize(person.firstname);
		}
		if (person.lastname) {
			str += ' ' + capitalize(person.lastname);
		}
		return str.trim();
	}

	return '';
}

function playerFullname(user?: IPlayer | IUserDB | null): string {
	if (user) {
		let str = '';

		if ('external_firstname' in user) {
			if (user.external_firstname) {
				str += capitalize(user.external_firstname);
			}
			if (user.external_lastname) {
				str += ' ' + capitalize(user.external_lastname);
			}
		} else if ('firstname' in user) {
			if (user.firstname) {
				str += capitalize(user.firstname);
			}
			if (user.lastname) {
				str += ' ' + capitalize(user.lastname);
			}
		}

		return str.trim();
	}

	return '';
}

function getPlayerPosition(player?: IPlayer | null): string {
	let position = '';

	if (player?.external_position1_name) {
		position = player.external_position1_name;

		if (player?.external_position2_name) {
			position = position + ' / ' + player.external_position2_name;

			return position;
		}
	}

	if (player?.external_position2_name) {
		position = player.external_position2_name;
	}

	return position;
}
