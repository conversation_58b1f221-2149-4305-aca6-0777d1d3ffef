import axios from "axios";
import type {
    AnalyticsData, BestPlayerResult,
    IOrganization,
    IOrganizationDB,
    IContact,
    ILegalsDB, IMatchDB,
    IPlayer,
    IPlayerDB,
    IUser,
    IUserDB, objectId, IPlayerVector, IShortlistDB, ISearchSessionDB, PlayerCompleteStat
} from "@shared/types";
import {AnalyticsType, DBCollection, LegalType, SortOrder} from "@shared/enums";
import {event, pageview} from "vue-gtag";
import {INegotiationDB, PaginationOption, SortField, StatSchema} from "@shared/types";
import {PaginatedList} from "@/front-types";

export type UseApiResult = {

    // --- user
    loadUserByToken: () => Promise<IUserDB>
    updateCurrentUser: (data: any) => Promise<IUserDB>
    currentUserAskPersonalData: () => Promise<void>
    deleteCurrentUser: () => Promise<boolean>


    // --- analytics
    sendAnalytics: (data: AnalyticsData) => Promise<void>

    // --- site
    registerToNewsletter: (email: string) => Promise<boolean>
    sendContactMessage: (data: IContact) => Promise<boolean>

    // --- legals
    loadLegals: (type: LegalType) => Promise<ILegalsDB>

    // --- helpers
    buildStaticUrl: (url: string) => string

    // --- search
    loadSearchSessions: () => Promise<ISearchSessionDB[]>
    loadSearchSession: (id: string, options?: {loadPlayers?: boolean}) => Promise<ISearchSessionDB>
    startSearchSession: (data:{player?: string, prompt?: string}) => Promise<ISearchSessionDB>
    continueSearchSession: (id: string, data:{prompt?: string, automation?: boolean}) => Promise<ISearchSessionDB>
    updateSearchSession: (id: string, data: {name: string}) => Promise<ISearchSessionDB>
    deleteSearchSession: (id: string) => Promise<boolean>

    // --- player
    getPlayer: (limit?: number, offset?: number, agencyId?: objectId) => Promise<{__pagination: any, __list: IPlayerDB[]}>
    getPlayerById: (id: string) => Promise<IPlayerDB>
    getPlayerByToken: (token: string) => Promise<IPlayerDB>
    createPlayer: (player: IPlayerDB) => Promise<IPlayerDB>
    updatePlayer: (player: Partial<IPlayer>, id: string) => Promise<IPlayerDB>
    deletePlayer: (id: string) => Promise<IPlayerDB>
    addPlayerToAgency: (player: any) => Promise<any>
    confirmPlayerToAgency: (player: IPlayerDB) => Promise<any>
    deletePlayerFromAgency: (playerId: objectId) => Promise<IPlayerDB>
    searchPlayerBySlug: (slug: string, itemByPage?: number, currentPage?: number, agencyId? : objectId) =>  Promise<{list: IPlayerDB[], pagination:{ offset: number, limit: number, total: number }}>
    getPlayerStatByMatch: (playerId: string, itemByPage?: number, currentPage?: number) => Promise<StatSchema[]>
    getPlayerStatBySeason: (playerId: string, itemByPage?: number, currentPage?: number) => Promise<StatSchema[]>
    getOrganizationPlayers: (organizationId?: objectId) => Promise<{players: IPlayerDB[], oldPlayers: IPlayerDB[]}>

    searchTournamentPlayers: (tournamentId: string, seasonId?: string|null, pagination?: any) => Promise<PaginatedList<IPlayerDB>>
    updatePlayerSalaries: (data: any) => Promise<boolean>
    getPlayerCompleteStat: (playerId: string) => Promise<PlayerCompleteStat>

    // --- match
    getMatchByExternalId : (id: string) => Promise<IMatchDB>
    // --- agency
    getAgency: () => Promise<IOrganizationDB[]>
    getAgencyById: (id: string) => Promise<IOrganizationDB>
    createAgency: (agency: IOrganization) => Promise<IOrganizationDB>
    updateAgency: (agency: IOrganization, id: string) => Promise<IOrganizationDB>
    deleteAgency: (id: string) => Promise<IOrganizationDB>
    // --- club
    getClubById: (id: string) => Promise<IOrganizationDB>
    createClub: (agency: IOrganization) => Promise<IOrganizationDB>
    updateClub: (agency: IOrganization, id: string) => Promise<IOrganizationDB>
    deleteClub: (id: string) => Promise<IOrganizationDB>

    // --- user
    createUser: (user: IUser) => Promise<IUserDB>
    getUsersByAgency: () => Promise<IUserDB[]>
    deleteUser: (id: string) => Promise<IUserDB>
    updateUser: (user: IUser, id: string) => Promise<IUserDB>
    inviteUser: (user: IUser) => Promise<IUserDB>
    getInvitedUserById: (id: string) => Promise<IUserDB>
    finalizeInvitation: (id: string, data: any) => Promise<{ token: string }>

    // --- history
    getHistory: () => Promise<INegotiationDB[]>

    // --- crud
    crudList: <T>(Collection: DBCollection, options?: {filters?: any, sort?: SortField[], pagination?: PaginationOption, projection?: string[], population?: string[]}) => Promise<T[]|PaginatedList<T>>

    // --- AI
    aiGetWelcomeMessage: (language?: string) => Promise<string|null>
    aiGetBestPlayers: (question: string, language?: string, options?: any) => Promise<BestPlayerResult>
    searchSimilarity: (data: IPlayerVector) => Promise<{similarity: number, player: IPlayerDB}[]>

    // --- API
    getSireneInfo: (siret: string) => Promise<any>

    // --- Shortlist
    createShortlist: (data: {name: string}) => Promise<IShortlistDB>
    loadShortlists: () => Promise<{mine: IShortlistDB[], shared: IShortlistDB[]}>
    loadShortlist: (id: string, options?: {players?: boolean, sharedWith?: boolean}) => Promise<IShortlistDB>
    updateShortlist: (id: string, data: {name: string}, options?: {players?: boolean, sharedWith?: boolean}) => Promise<IShortlistDB>
    deleteShortlist: (id: string) => Promise<boolean>
    addPlayerToShortlist: (shortlistId: string, playerId: string) => Promise<IShortlistDB>
}

export function useApi(): UseApiResult {
    return {
        // --- user
        loadUserByToken,
        updateCurrentUser,
        currentUserAskPersonalData,
        deleteCurrentUser,

        // --- analytics
        sendAnalytics,

        // --- site
        registerToNewsletter,
        sendContactMessage,

        // --- legals
        loadLegals,

        // --- helpers
        buildStaticUrl,

        // --- search
        loadSearchSessions,
        loadSearchSession,
        startSearchSession,
        continueSearchSession,
        updateSearchSession,
        deleteSearchSession,

        // --- player
        getPlayer,
        getPlayerById,
        getPlayerByToken,
        createPlayer,
        updatePlayer,
        deletePlayer,
        searchPlayerBySlug,
        addPlayerToAgency,
        confirmPlayerToAgency,
        deletePlayerFromAgency,
        getPlayerStatByMatch,
        getPlayerStatBySeason,
        getOrganizationPlayers,

        searchTournamentPlayers,
        updatePlayerSalaries,

        getPlayerCompleteStat,

        // --- match
        getMatchByExternalId,

        // --- agency
        getAgency,
        getAgencyById,
        createAgency,
        updateAgency,
        deleteAgency,

        // --- club
        getClubById,
        createClub,
        updateClub,
        deleteClub,

        // --- user
        createUser,
        getUsersByAgency,
        deleteUser,
        updateUser,
        inviteUser,
        getInvitedUserById,
        finalizeInvitation,

        // --- history
        getHistory,

        // --- crud
        crudList,

        // --- AI
        aiGetWelcomeMessage,
        aiGetBestPlayers,
        searchSimilarity,

        // --- API
        getSireneInfo,

        // --- shortlists
        createShortlist,
        loadShortlists,
        loadShortlist,
        updateShortlist,
        deleteShortlist,
        addPlayerToShortlist,
    }
}

// -----------------------------------------------------------------------------------
// USER

async function loadUserByToken(): Promise<IUserDB> {
    const response = await axios.get(buildUrl("/current-user"));
    if(response.data) {
        return response.data;
    }
    else {
        throw "not.found";
    }
}

async function updateCurrentUser(data: any): Promise<IUserDB> {
    try {
        const result = await axios.put(buildUrl("/current-user"), data);
        return result.data;
    }
    catch (err: any) {
        console.log("updateCurrentUser Error: ", err);
        throw err.response.data;
    }
}

async function currentUserAskPersonalData(): Promise<void> {
    await axios.post(buildUrl("/current-user/ask-data"));
}

async function deleteCurrentUser(): Promise<boolean> {
    const response = await axios.delete(buildUrl("/current-user"));
    if(response.data) {
        return !!response.data.success;
    }
    else {
        return false;
    }
}
// -----------------------------------------------------------------------------------
// ANALYTICS

async function sendAnalytics(data: AnalyticsData): Promise<void> {
    try {
        if(import.meta.env.VITE_GTAG && import.meta.env.VITE_GTAG.trim().length > 0) {
            // use GTag
            if (data.type === AnalyticsType.page) {
                pageview({
                    page_path: data.action,
                    page_title: data.data?.title,
                    page_location: data.location
                });
            }
            else {
                event(data.action, data.data ? {...data.data, screen_name: data.location} : {screen_name: data.location});
            }
        }
        else {
            await axios.post(buildUrl("/analytics"), data);
        }
    }
    catch(err) {
        console.log("ERROR Api (sendAnalytics): ", err);
    }
}

// -----------------------------------------------------------------------------------
// SITE

async function registerToNewsletter(email: string): Promise<boolean> {
    try {
        const result = await axios.post(buildUrl("/newsletter"), {email});
        return result.data;
    }
    catch(err: any) {
        console.log("registerToNewsletter Error: ", err);
        throw err.response.data;
    }
}

async function sendContactMessage(data: IContact): Promise<boolean> {
    try {
        const result = await axios.post(buildUrl("/contact"), data);
        return result.data;
    }
    catch(err: any) {
        console.log("sendContactMessage Error: ", err);
        throw err.response.data;
    }
}

// -----------------------------------------------------------------------------------
// LEGALS

async function loadLegals(type: LegalType): Promise<ILegalsDB> {
    const result = await axios.get(buildUrl(`/site/legals/${type}`));
    return result.data;
}

// -----------------------------------------------------------------------------------
// HELPERS

function buildUrl(url: string, query?: any): string {
    let fullUrl = import.meta.env.VITE_API_URL + url;
    if(query) {
        let separator = "?";
        for(const key of Object.keys(query)) {
            const value = query[key] ? encodeURIComponent(query[key].toString()) : null;
            if(value !== null) {
                fullUrl += separator + key + "=" + encodeURIComponent(query[key].toString());
                separator = "&";
            }
        }
    }
    return fullUrl;
}

function buildStaticUrl(url: string): string {
    return import.meta.env.VITE_API_STATIC_URL + ((url && url.startsWith("/"))?"":"/") + url;
}

// -----------------------------------------------------------------------------------
// PLAYER

async function getPlayer(limit?: number, offset?: number, agencyId?: objectId): Promise<{__pagination: any, __list: IPlayerDB[]}> {
    const result = await axios.get(buildUrl(`/crud/player?${agencyId ? `agencyId=${agencyId}&` : ''}${limit ? `__limit=${limit}` : ''}${offset ? `&__offset=${offset}` : ''}`));
    return result.data;
}

async function getPlayerById(id: string): Promise<IPlayerDB> {
    const result = await axios.get(buildUrl(`/crud/player/${id}`));
    return result.data;
}
async function getPlayerByToken(token: string): Promise<IPlayerDB> {
    const result = await axios.get(buildUrl(`/player-by-token/${token}`));
    return result.data;
}

async function createPlayer(player: IPlayer): Promise<IPlayerDB> {
    const result = await axios.post(buildUrl("/crud/player"), player);
    return result.data;
}

async function updatePlayer(player: Partial<IPlayer>, id: string): Promise<IPlayerDB> {
    const result = await axios.put(buildUrl(`/crud/player/${id}`), player);
    return result.data;
}

async function deletePlayer(id: string): Promise<IPlayerDB> {
    const result = await axios.delete(buildUrl(`/crud/player/${id}`));
    return result.data;
}

async function addPlayerToAgency(player: any): Promise<any> {
    const result = await axios.put(buildUrl(`/add-player/`), player);
    return result.data;
}
async function confirmPlayerToAgency(player: IPlayerDB): Promise<any> {
    const result = await axios.put(buildUrl(`/confirm-player/`), player);
    return result.data;
}

async function deletePlayerFromAgency(playerId: objectId): Promise<IPlayerDB> {
    const result = await axios.put(buildUrl(`/delete-player/` + playerId));
    return result.data;
}

async function searchPlayerBySlug(slug: string, itemByPage?: number, offset?: number, agencyId? : objectId):
    Promise<{ list: IPlayerDB[], pagination: { offset: number, limit: number, total: number }}> {
    const result = await axios.get(buildUrl(
        `/player-by-slug/${slug}?${agencyId ? `agencyId=${agencyId}&` : ''}${itemByPage ? `__limit=${itemByPage}` : ''}${offset ? `&__offset=${offset}` : ''}`));
    return result.data;
}

async function getPlayerStatByMatch(playerId: string, itemByPage?: number, offset?: number): Promise<StatSchema[]> {
    const result = await axios.get(buildUrl(`/player-stat-by-match/${playerId}?${itemByPage ? `__limit=${itemByPage}` : ''}${offset ? `&__offset=${offset}` : ''}`));
    return result.data;
}

async function getPlayerStatBySeason(playerId: string, itemByPage?: number, offset?: number): Promise<StatSchema[]> {
    const result = await axios.get(buildUrl(`/player-stat-by-season/${playerId}?${itemByPage ? `__limit=${itemByPage}` : ''}${offset ? `&__offset=${offset}` : ''}`));
    return result.data;
}

async function getOrganizationPlayers(organizationId?: objectId): Promise<{players: IPlayerDB[], oldPlayers: IPlayerDB[]}> {
    const result = await axios.get(buildUrl(`/organization/${organizationId}/players`));
    return result.data;
}

async function searchTournamentPlayers(tournamentId: string, seasonId?: string|null, pagination?: any): Promise<PaginatedList<IPlayerDB>> {
    const result = await axios.get(buildUrl(`/players/tournament`, {tournamentId, seasonId, __limit: pagination?.limit, __offset: pagination?.offset}));
    return result.data;
}

async function updatePlayerSalaries(data: any): Promise<boolean> {
    const result = await axios.post(buildUrl(`/players/salaries`), data);
    return result.data.success;
}

async function getPlayerCompleteStat(playerId: string) : Promise<PlayerCompleteStat> {
    const result = await axios.get(buildUrl(`/player/${playerId}/complete-stat`));
    return result.data;
}

// -----------------------------------------------------------------------------------
// MATCH
async function getMatchByExternalId(id: string): Promise<IMatchDB> {
    const result = await axios.get(buildUrl(`/match/${id}`));
    return result.data;
}

//-----------------------------------------------------------------------------------
// AGENCY

async function getAgency(): Promise<IOrganizationDB[]> {
    const result = await axios.get(buildUrl("/crud/agency"));
    return result.data;
}

async function getAgencyById(id: string): Promise<IOrganizationDB> {
    const result = await axios.get(buildUrl(`/crud/organization/${id}`));
    return result.data;
}

async function createAgency(agency: IOrganization): Promise<IOrganizationDB> {
    const result = await axios.post(buildUrl("/crud/organization"), agency);
    return result.data;
}

async function updateAgency(agency: IOrganization, id: string): Promise<IOrganizationDB> {
    const result = await axios.put(buildUrl(`/crud/organization/${id}`), agency);
    return result.data;
}

async function deleteAgency(id: string): Promise<IOrganizationDB> {
    const result = await axios.delete(buildUrl(`/crud/agency/${id}`));
    return result.data;
}

//-----------------------------------------------------------------------------------
// AGENCY

async function getClubById(id: string): Promise<IOrganizationDB> {
    const result = await axios.get(buildUrl(`/crud/organization/${id}`));
    return result.data;
}

async function createClub(agency: IOrganization): Promise<IOrganizationDB> {
    const result = await axios.post(buildUrl("/crud/organization"), agency);
    return result.data;
}

async function updateClub(agency: IOrganization, id: string): Promise<IOrganizationDB> {
    const result = await axios.put(buildUrl(`/crud/organization/${id}`), agency);
    return result.data;
}

async function deleteClub(id: string): Promise<IOrganizationDB> {
    const result = await axios.delete(buildUrl(`/crud/agency/${id}`));
    return result.data;
}

// -----------------------------------------------------------------------------------
// USER

async function createUser(user: IUser): Promise<IUserDB> {
    const result = await axios.post(buildUrl(`/crud/user`), user);
    return result.data;
}

async function getUsersByAgency(): Promise<IUserDB[]> {
    const result = await axios.get(buildUrl(`/users`));
    return result.data;
}

async function deleteUser(id: string): Promise<IUserDB> {
    const result = await axios.delete(buildUrl(`/crud/user/${id}`));
    return result.data;
}

async function updateUser(user: IUser, id: string): Promise<IUserDB> {
    const result = await axios.put(buildUrl(`/crud/user/${id}`), user);
    return result.data;
}

async function inviteUser(user: IUser): Promise<IUserDB> {
    try {
        const result = await axios.post(buildUrl(`/user/invitation`), user);
        return result.data;
    }
    catch (err: any) {
        console.log("Invite User Error: ", err);
        throw err.response.data;
    }
}

async function getInvitedUserById(id: string): Promise<IUserDB> {
    const result = await axios.get(buildUrl(`/user/invited/${id}`));
    return result.data;
}

async function finalizeInvitation(id: string, data: any): Promise<{ token: string }> {
    try {
        const result = await axios.post(buildUrl(`/user/invited/${id}`), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Finalize invitation Error: ", err);
        throw err.response.data;
    }
}

// -----------------------------------------------------------------------------------
// HISTORY

async function getHistory(): Promise<INegotiationDB[]> {
    const result = await axios.get(buildUrl(`/history/`));
    return result.data;
}

// -----------------------------------------------------------------------------------
// AI

async function aiGetWelcomeMessage(language?: string): Promise<string|null> {
    const result = await axios.get(buildUrl(`/ai/welcome-message`, {lang: language}));
    return result.data.message;
}

async function aiGetBestPlayers(question: string, language?: string, options?: any): Promise<BestPlayerResult> {
    const result = await axios.post(buildUrl(`/ai/best-players`, {lang: language}), {
        question,
        ...(options ?? {})
    });
    return result.data;
}

async function searchSimilarity(data: IPlayerVector): Promise<{similarity: number, player: IPlayerDB}[]> {
    try {
        const result = await axios.post(buildUrl(`/ai/search-similarity`), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Finalize invitation Error: ", err);
        throw err.response.data;
    }
}
// -----------------------------------------------------------------------------------
// API

async function getSireneInfo(siret: string): Promise<any> {
    const result = await axios.get(buildUrl(`/api/sirene/${siret}`));
    return result.data;
}

// -----------------------------------------------------------------------------------
// SEARCH

async function loadSearchSessions(): Promise<ISearchSessionDB[]> {
    const result = await axios.get(buildUrl("/search-sessions"));
    return result.data;
}

async function loadSearchSession(id: string, options?: {loadPlayers?: boolean, loadUsers?: boolean}): Promise<ISearchSessionDB> {
    const result = await axios.get(buildUrl(`/search-sessions/${id}`, options));
    return result.data;
}

async function deleteSearchSession(id: string): Promise<boolean> {
    const result = await axios.delete(buildUrl(`/search-sessions/${id}`));
    return result.data.success;
}

async function startSearchSession(data: {player?: string, prompt?: string}): Promise<ISearchSessionDB> {
    try {
        const result = await axios.post(buildUrl("/search-sessions"), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Unable to start search session: ", err);
        throw err.response.data;
    }
}

async function continueSearchSession(id: string, data: {prompt?: string, automation?: boolean}): Promise<ISearchSessionDB> {
    try {
        const result = await axios.put(buildUrl(`/search-sessions/${id}`), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Unable to continue search session: ", err);
        throw err.response.data;
    }
}

async function updateSearchSession(id: string, data: any): Promise<ISearchSessionDB> {
    try {
        const result = await axios.put(buildUrl(`/search-sessions/${id}`), data);
        return result.data;
    }
    catch (err: any) {
        console.log("updateSearchSession Error: ", err);
        throw err.response.data;
    }
}

// -----------------------------------------------------------------------------------
// ShortList

async function createShortlist(data: {name: string}): Promise<IShortlistDB> {
    try {
        const result = await axios.post(buildUrl("/shortlist"), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Unable to create shortlist: ", err);
        throw err.response.data;
    }
}

async function loadShortlists(): Promise<{mine: IShortlistDB[], shared: IShortlistDB[]}> {
    const result = await axios.get(buildUrl("/shortlists"));
    return result.data;
}

async function loadShortlist(id: string, options?: {players?: boolean, sharedWith?: boolean}): Promise<IShortlistDB> {
    const result = await axios.get(buildUrl(`/shortlist/${id}`, options));
    return result.data;
}


async function updateShortlist(id: string, data: {name: string}, options?: {players?: boolean, sharedWith?: boolean}): Promise<IShortlistDB> {
    try {
        const result = await axios.put(buildUrl(`/shortlist/${id}`, options), data);
        return result.data;
    }
    catch (err: any) {
        console.log("Unable to create shortlist: ", err);
        throw err.response.data;
    }
}

async function deleteShortlist(id: string): Promise<boolean> {
    const result = await axios.delete(buildUrl(`/shortlist/${id}`));
    return result.data.success;
}

async function addPlayerToShortlist(shortlistId: string, playerId: string): Promise<IShortlistDB> {
    try {
        const result = await axios.post(buildUrl(`/shortlist/${shortlistId}/player`), {playerId});
        return result.data;
    }
    catch (err: any) {
        console.log("Unable to create shortlist: ", err);
        throw err.response.data;
    }
}


// -----------------------------------------------------------------------------------
// CRUD

async function crudList<T>(collection: DBCollection, options?: {filters?: any, sort?: SortField[], pagination?: PaginationOption, projection?: string[], population?: string[]}): Promise<T[]|PaginatedList<T>> {
    console.log("crudlist options: ", options);
    let query = crudBuildQuery(options);
    const result = await axios.get(buildUrl(`/crud/${collection}`, query));
    return result.data;
}

function crudBuildQuery(options?: {filters?: any, sort?: SortField[], projection?: string[], population?: string[], pagination?: PaginationOption}): any {
    let query: any = {};
    if(options) {
        if (options.filters) {
            query = options.filters;
        }
        if (options.population && options.population.length > 0) {
            query.__population = options.population.join(",");
        }
        if (options.projection && options.projection.length > 0) {
            query.__projection = options.projection.join(",");
        }
        if(options.sort && options.sort.length > 0) {
            query.__sort = options.sort.map(s => {
                let sortCode: string = "";
                switch(s.order) {
                    case SortOrder.desc:
                        sortCode = "-";
                        break;
                    case SortOrder.asc:
                        sortCode = "+";
                        break;
                    case SortOrder.random:
                        sortCode = "*";
                        break;
                    case SortOrder.none:
                        sortCode = "?";
                        break;
                }
                return sortCode + s.field;
            });
        }
        if(options.pagination) {
            query.__limit = options.pagination.limit;
            query.__offset = options.pagination.offset;
        }
    }

    return query;
}

// -----------------------------------------------------------------------------------
// DEFAULT VALUES
