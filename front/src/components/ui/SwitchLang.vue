<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import IEn from '@/components/images/lang/IEn.vue';
import IFr from '@/components/images/lang/IFr.vue';

const { locale } = useI18n();
const currentLanguage = ref(locale.value);

function switchLanguage() {
	currentLanguage.value = currentLanguage.value === 'fr' ? 'en' : 'fr';
	locale.value = currentLanguage.value;
	localStorage.setItem('language', currentLanguage.value);
}
</script>
<template>
	<div class="switchLang" @click="switchLanguage">
		<IFr v-if="currentLanguage === 'fr'" />
		<IEn v-if="currentLanguage === 'en'" />
		<div class="name">{{ currentLanguage }}</div>
	</div>
</template>

<style lang="scss">
.switchLang {
	display: flex;
	height: fit-content;
	gap: 15px;
	padding: 6px 12px;
	align-items: center;
	border-radius: 8px;
	cursor: pointer;
	background-color: $white;

	.name {
		text-transform: uppercase;
		font-size: 12px;
		font-weight: 600;
		color: #00005b;
	}
}
</style>
