Ihome.vue<script setup lang="ts">

</script>

<template>
  <svg class="icon ihistory" xmlns="http://www.w3.org/2000/svg" width="14" height="20" viewBox="0 0 14 20" fill="none">
    <path d="M11.9911 13.67L7.95105 10H5.24105L1.20105 13.67C0.0710522 14.69 -0.298948 16.26 0.251052 17.68C0.801052 19.09 2.14105 20 3.65105 20H9.54105C11.0611 20 12.3911 19.09 12.9411 17.68C13.4911 16.26 13.1211 14.69 11.9911 13.67ZM8.42105 16.14H4.78105C4.40105 16.14 4.10105 15.83 4.10105 15.46C4.10105 15.09 4.41105 14.78 4.78105 14.78H8.42105C8.80105 14.78 9.10105 15.09 9.10105 15.46C9.10105 15.83 8.79105 16.14 8.42105 16.14Z" fill="#B1B1B1"/>
    <path d="M12.9503 2.32C12.4003 0.91 11.0603 0 9.55026 0H3.65026C2.14026 0 0.800258 0.91 0.250258 2.32C-0.289742 3.74 0.0802583 5.31 1.21026 6.33L5.25026 10H7.96026L12.0003 6.33C13.1203 5.31 13.4903 3.74 12.9503 2.32ZM8.42026 5.23H4.78026C4.40026 5.23 4.10026 4.92 4.10026 4.55C4.10026 4.18 4.41026 3.87 4.78026 3.87H8.42026C8.80026 3.87 9.10026 4.18 9.10026 4.55C9.10026 4.92 8.79026 5.23 8.42026 5.23Z" fill="#B1B1B1"/>
  </svg>
</template>

<style scoped lang="scss">

</style>