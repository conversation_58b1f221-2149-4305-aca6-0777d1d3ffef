<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import FormPlus from "@/components/forms/FormPlus.vue";
import {ref} from "vue";
import FieldInput from "@/components/forms/FieldInput.vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";

const {t} = useI18n()

const props = defineProps<{
	errors: any
}>();
const emit = defineEmits<{
	(e: 'create', name: string): void
}>();

const name = ref<string>("");

function create() {
	emit("create", name.value);
}
</script>

<template>
	<modal class="ModalAddShortlist" :close-button="true" title-slug="shortlist.create.title">
		<form-plus :errors="errors" error-prefix="shortlist">
			<field-input name="name" v-model="name" label-slug="shortlist.fields.name.label" placeholder-slug="shortlist.fields.name.placeholder" />
		</form-plus>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button @click="create">{{ t('shortlist.action.create') }}</main-button>
			</controls>
		</template>
	</modal>
</template>

<style lang="scss">
.ModalAddShortlist {
	.ModalBox {
		width: 410px;
	}
}
</style>