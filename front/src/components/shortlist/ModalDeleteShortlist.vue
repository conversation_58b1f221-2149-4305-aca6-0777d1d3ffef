<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import {IShortlistDB} from "@shared/crudTypes";
import {computed, ref} from "vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";
import {useFormatters} from "@/composition/formatters";

const {t} = useI18n()

const props = defineProps<{
	shortlist: IShortlistDB
}>();
const emit = defineEmits<{
	(e: 'delete'): void
}>();

const {paragraphize} = useFormatters();

const name = ref<string>(props.shortlist.name);

const text = computed<string>(() => {
	return paragraphize(t('shortlist.delete.text', [props.shortlist.name])) || "";
});

function deleteShortlist() {
	emit("delete");
}
</script>

<template>
	<modal class="ModalDeleteShortlist" :close-button="true" title-slug="shortlist.delete.title">
		<div class="text" v-html="text"/>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button class="danger" @click="deleteShortlist">{{ t('shortlist.action.delete') }}</main-button>
			</controls>
		</template>
	</modal>
</template>

<style lang="scss">
.ModalDeleteShortlist {
	.ModalBox {
		width: 410px;

		.text {
			padding: 15px;
			border-radius: 10px;
			background-color: $cbackgroundGray;

			p {
				margin-top: 12px;
				color: $cdarktext;
				font-size: 16px;
				font-weight: 500;
				line-height: 1.3em;

				&:first-child {
					margin-top: 0;
				}

				b {
					font-weight: $fbold;
				}
			}
		}
	}
}
</style>