<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {IShortlistDB} from "@shared/crudTypes";
import Iplus from "@/components/images/Iplus.vue";
import {computed} from "vue";
import PlayerImage from "@/components/player/PlayerImage.vue";

const props = defineProps<{
	shortlist: IShortlistDB
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const pictures = computed<any[]>(() => {
	let pictures: any[] = [];
	let players = props.shortlist.playerObjects ?? [];
	for(let i = 0; i < 4; ++i) {
		if(i === 3 && players.length > 4) {
			pictures.push({more: players.length - 3});
			break;
		}

		if(i < players.length) {
			pictures.push({path: players[i].external_photo ?? (players[i].external_gender_name === 'M' ? '/images/player/men.png' : '/images/player/women.png')});
		}
		else {
			pictures.push({dummy: true});
		}
	}

	return pictures;
});
const filteredPlayers = computed<any[]>(() => {
	let filteredPlayers: any[] = [];
	let players = props.shortlist.playerObjects ?? [];
	for(let i = 0; i < 4; ++i) {
		if(i === 3 && players.length > 4) {
			filteredPlayers.push({more: players.length - 3});
			break;
		}

		if(i < players.length) {
			filteredPlayers.push({player: players[i]});
		}
		else {
			filteredPlayers.push({dummy: true});
		}
	}

	return filteredPlayers;
});
</script>

<template>
	<div class="ShortlistCard">
		<h2 class="title">{{ shortlist.name }}</h2>
		<div class="pictures">
			<div class="picture" v-for="(p, idx) in filteredPlayers" :key="'p-' + idx">
				<player-image v-if="p.player" :player="p.player"/>
				<div v-else-if="p.more" class="more"><iplus/><span>{{ t('shortlist.more', p.more) }}</span></div>
				<div v-else class="dummy"/>
			</div>
		</div>
	</div>
</template>

<style lang="scss">
.ShortlistCard {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10px;
	border: 1px solid $cborderGray;
	background-color: white;
	border-radius: 10px;
	padding: 15px;
	cursor: pointer;

	h2 {
		font-size: 14px;
		font-weight: $fbold;
		color: $cgrayBlue;
		margin-bottom: $gap2;
	}

	.pictures {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 10px;
		width: 100%;

		.picture {
			width: 100%;
			aspect-ratio: 1;
			border-radius: 10px;
			overflow: hidden;

			.dummy {
				width: 100%;
				height: 100%;
				background-color: $cborderGray;
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.more {
				width: 100%;
				height: 100%;

				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				gap: 10px;

				.icon {
					width: 20px;
					height: 20px;
					color: $cgrayBlue;
				}

				span {
					color: $cgrayBlue;
					font-size: 12px;
					font-weight: $fregular;
				}
			}
		}
	}
}
</style>