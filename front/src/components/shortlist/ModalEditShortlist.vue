<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import {IShortlistDB} from "@shared/crudTypes";
import FormPlus from "@/components/forms/FormPlus.vue";
import {ref} from "vue";
import FieldInput from "@/components/forms/FieldInput.vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";

const {t} = useI18n()

const props = defineProps<{
	shortlist: IShortlistDB
	errors: any
}>();
const emit = defineEmits<{
	(e: 'edit', name: string): void
}>();

const name = ref<string>(props.shortlist.name);

function update() {
	emit("edit", name.value);
}
</script>

<template>
	<modal class="ModalEditShortlist" :close-button="true" title-slug="shortlist.update.title">
		<form-plus :errors="errors" error-prefix="shortlist">
			<field-input name="name" v-model="name" label-slug="shortlist.fields.name.label" placeholder-slug="shortlist.fields.name.placeholder" />
		</form-plus>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button @click="update">{{ t('shortlist.action.update') }}</main-button>
			</controls>
		</template>
	</modal>
</template>

<style lang="scss">
.ModalEditShortlist {
	.ModalBox {
		width: 410px;
	}
}
</style>