<script setup lang="ts">
import { CheckIcon, ChevronDown, ChevronUp } from 'lucide-vue-next';
import { CheckboxRoot, PopoverAnchor, PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger } from 'radix-vue';
import { computed, ref } from 'vue';

import { FilterItem } from '@/common';

import { Button, Container, Typography } from '../ui';

const {
	label,
	options,
	customOffset = 10,
} = defineProps<{
	label: string;
	options: FilterItem[];
	customOffset?: number;
}>();

const modelValue = defineModel<string[]>();

const isOpen = ref(false);

const chevronIcon = computed(() => (isOpen.value ? ChevronUp : ChevronDown));
</script>

<template>
	<PopoverRoot v-model:open="isOpen" class="'popover'">
		<PopoverTrigger class="popover__trigger" asChild>
			<Button variant="secondary">
				<Typography size="md" variant="medium">{{ label }}</Typography>
				<template #icon-right>
					<slot name="icon-right">
						<component :is="chevronIcon" :size="20" color="#535f7a" />
					</slot>
				</template>
			</Button>
		</PopoverTrigger>
		<PopoverAnchor />
		<PopoverPortal>
			<PopoverContent :alignOffset="100" :sideOffset="customOffset">
				<Container class="popover__content">
					<CheckboxRoot
						v-for="option in options"
						class="popover__content__checkbox"
						asChild
						:key="option.value"
						:checked="modelValue?.includes(option.value)"
						@update:checked="
							(checked) => {
								if (checked) {
									modelValue = [...(modelValue ?? []), option.value];
								} else {
									modelValue = (modelValue ?? []).filter((v) => v !== option.value);
								}
							}
						"
					>
						<Button variant="ghost">
							<template #icon-left
								><Container class="popover__content__checkbox__icon"
									><CheckIcon color="white" :size="16" /></Container
							></template>
							<Typography size="md">{{ option.label }}</Typography>
						</Button>
					</CheckboxRoot>
				</Container>
			</PopoverContent>
		</PopoverPortal>
	</PopoverRoot>
</template>

<style lang="scss" scoped>
.popover {
	&__trigger {
		width: fit-content;
		background-color: $white;
		text-wrap: nowrap;
	}

	&__content {
		display: flex;
		flex-direction: column;
		background-color: $white;
		padding: 8px 12px;
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
		&__checkbox {
			background-color: transparent;
			border: none;

			&[data-state='checked'] {
				.popover__content__checkbox__icon {
					background-color: black;
				}
			}

			&:hover {
				background-color: $grey-light;
			}

			&__icon {
				border: black solid 2px;
				background-color: $white;
				align-items: center;
				border-radius: 6px;
				padding: 1px;
			}
		}
	}
}
</style>
