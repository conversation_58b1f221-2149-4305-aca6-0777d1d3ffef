<script setup lang="ts">
import { ChevronDown, ChevronUp } from 'lucide-vue-next';
import { CollapsibleContent, CollapsibleRoot, CollapsibleTrigger } from 'radix-vue';
import { computed, ref } from 'vue';

import { Button, Typography } from '../ui';

const { label } = defineProps<{
	label: string;
}>();

const modelValue = defineModel<string[]>();

const isOpen = ref(false);

const chevronIcon = computed(() => (isOpen.value ? ChevronUp : ChevronDown));
</script>

<template>
	<CollapsibleRoot v-model:open="isOpen" class="collapsible">
		<CollapsibleTrigger class="collapsible__trigger" asChild>
			<Button variant="secondary">
				<Typography size="md" variant="medium">{{ label }}</Typography>
				<template #icon-right>
					<slot name="icon-right">
						<component :is="chevronIcon" :size="20" color="#535f7a" />
					</slot>
				</template>
			</Button>
		</CollapsibleTrigger>
		<CollapsibleContent>
			<slot name="content" />
		</CollapsibleContent>
	</CollapsibleRoot>
</template>

<style lang="scss" scoped>
.collapsible {
	&__trigger {
		width: fit-content;
		background-color: $white;
	}

	&__content {
		display: flex;
		flex-direction: column;
		background-color: $white;
		padding: 8px 12px;
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
	}
}
</style>
