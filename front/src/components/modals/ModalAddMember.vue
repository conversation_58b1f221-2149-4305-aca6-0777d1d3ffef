<script setup lang="ts">
import { IUserDB } from '@shared/crudTypes';
import { useI18n } from 'vue-i18n';

import MemberForm from '@/components/forms/MemberForm.vue';
import Modal from '@/components/ui/Modal.vue';
import UnderlineTitle from '@/components/ui/UnderlineTitle.vue';

const { t } = useI18n();

const props = defineProps<{
	member: IUserDB | undefined;
}>();

const emit = defineEmits<{
	(e: 'update'): void;
}>();
</script>

<template>
	<modal class="membersModal" :close-button="true">
		<template #default="{ close }">
			<underline-title>{{ t('user.fields.title') }}</underline-title>
			<MemberForm :member="member" @close="close" @update="emit('update')" />
		</template>
	</modal>
</template>

<style lang="scss">
.membersModal {
	.ModalBox {
		width: 410px;
	}
}
</style>
