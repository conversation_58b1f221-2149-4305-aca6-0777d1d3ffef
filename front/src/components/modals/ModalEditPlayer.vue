<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";

import updatePlayer from "@/components/forms/updatePlayer.vue";

const props = defineProps<{
  player: any
}>();

</script>

<template>
  <modal class="modalEditPlayer" :closeButton="true">
    <template #default="{close}">
      <updatePlayer :player="player" @close="close"/>
    </template>
  </modal>
</template>

<style  lang="scss">
.modalEditPlayer {
  .ModalBox {
    width: 410px;
  }
}
</style>