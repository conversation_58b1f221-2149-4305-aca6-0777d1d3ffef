<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {computed} from "vue";
import {useI18n} from "vue-i18n";
import {useFormatters} from "@/composition/formatters";
const props = defineProps<{
  content: any
}>();

const emit = defineEmits<{
  (e: 'close'): void
}>();

const {t} = useI18n();
const {paragraphize} = useFormatters();
const text: any = computed<string|undefined>(() => {
  if(props.content) {
    console.log("Content: ", props.content);
    const text = props.content.text ? props.content.text : (props.content.textSlug ? t(props.content.textSlug) : undefined);
    if (text) {
      return paragraphize(text) || undefined;
    }
    else {
      return text;
    }
  }
  else {
    return text;
  }
});
</script>

<template>
  <modal v-if="content" class="ModalConfirmation" :title="content.title" :titleSlug="content.titleSlug" :closeButton="true" @close="emit('close')">
    <div v-if="text" class="text" v-html="text"/>
    <slot v-else />
  </modal>
</template>

<style lang="scss">
.ModalConfirmation {
  .ModalBox {
    width: 500px;
      .content {
        text-align: center;
      }
    .ModalFooter {
      .Controls {
        margin-bottom: 0;
      }
    }
  }
  .text {
    color: $cmainColor;
    font-size: 15px;
    line-height: 1.3em;

    p {
      margin-top: 12px;

      &:first-child {
        margin-top: 0;
      }

      b {
        font-weight: $fbold;
      }
    }
  }

  .FormPlus {
    margin-top: $gap;
    padding: $gap2;
    background-color: $cmainBackground;
  }

}
</style>