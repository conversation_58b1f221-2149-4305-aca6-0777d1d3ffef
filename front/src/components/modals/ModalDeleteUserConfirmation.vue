<script setup lang="ts">
import { useI18n } from 'vue-i18n';

import GreyBox from '@/components/ui/GreyBox.vue';
import MainButton from '@/components/ui/MainButton.vue';
import Modal from '@/components/ui/Modal.vue';
import Title from '@/components/ui/UnderlineTitle.vue';

const { t } = useI18n();

const emit = defineEmits<{
	(e: 'delete'): void;
}>();

function handleConfirmClick(close: any) {
	emit('delete');
	close();
}
</script>

<template>
	<modal class="modalDeleteUserConfirmation" :closeButton="true">
		<template #default="{ close }">
			<Title>{{ t('user.modal.title') }}</Title>
			<GreyBox>
				<p v-html="t('user.modal.text')" />
			</GreyBox>

			<div class="modalButton">
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button @click="handleConfirmClick(close)">{{ t('global.confirm') }}</main-button>
			</div>
		</template>
	</modal>
</template>

<style lang="scss">
.modalDeleteUserConfirmation {
	.ModalBox {
		width: 410px;

		.modalButton {
			display: flex;
			justify-content: space-between;
			margin-top: 30px;
			gap: 30px;

			.MainButton {
				padding: 15px 20px;
				flex: 1;
			}
		}
	}
}
</style>
