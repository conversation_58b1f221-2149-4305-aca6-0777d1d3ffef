<script setup lang="ts">
import { objectId } from '@shared/baseTypes';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import FieldInput from '@/components/forms/FieldInput.vue';
import FormPlus from '@/components/forms/FormPlus.vue';
import GreyBox from '@/components/ui/GreyBox.vue';
import MainButton from '@/components/ui/MainButton.vue';
import Modal from '@/components/ui/Modal.vue';
import Title from '@/components/ui/UnderlineTitle.vue';
import { useApi } from '@/composition/api';

const { t } = useI18n();
const { addPlayerToAgency, deletePlayerFromAgency } = useApi();

const props = defineProps<{
	playerId: objectId;
	isAdd: boolean;
}>();

const emit = defineEmits<{
	(e: 'changeStatus', id?: any): void;
}>();

const player = ref<any>({
	_id: props.playerId ? props.playerId : '',
	email: '',
	phone: '',
});

function handleCancelClick(close: any) {
	close();
}

async function handleConfirmClick(close: any) {
	if (props.isAdd) {
		const id = await addPlayerToAgency(player.value); // player info
		emit('changeStatus', id);
	} else {
		await deletePlayerFromAgency(props.playerId);
		emit('changeStatus');
	}
	close();
}
</script>

<template>
	<modal class="modalAddPlayerConfirmation" :closeButton="true">
		<template #default="{ close }">
			<Title>{{ isAdd ? t('player.modal.title') : t('player.modalDelete.title') }}</Title>
			<GreyBox>
				<p v-html="isAdd ? t('player.modal.text') : t('player.modalDelete.text')" />
			</GreyBox>
			<FormPlus v-if="isAdd" @submit="handleConfirmClick">
				<field-input :labelSlug="t('user.fields.email.label')" name="lastname" v-model="player.email" />
				<field-input :labelSlug="t('user.fields.phone.label')" name="lastname" v-model="player.phone" />
			</FormPlus>
			<div class="modalButton">
				<main-button class="grey" @click="handleCancelClick(close)">{{ t('global.cancel') }}</main-button>
				<main-button @click="handleConfirmClick(close)">{{ t('player.modal.confirm') }}</main-button>
			</div>
		</template>
	</modal>
</template>

<style lang="scss">
.modalAddPlayerConfirmation {
	.ModalBox {
		width: 410px;

		.modalButton {
			display: flex;
			justify-content: space-between;
			margin-top: 30px;

			.MainButton {
				padding: 15px 20px;
			}
		}
	}
}
</style>
