<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import MainButton from "@/components/ui/MainButton.vue";
import Controls from "@/components/ui/Controls.vue";
import type {Confirmation} from "@/front-types";
import {computed, ref} from "vue";
import {useI18n} from "vue-i18n";
import {useFormatters} from "@/composition/formatters";
import FieldCheckbox from "@/components/forms/FieldCheckbox.vue";
import FormPlus from "@/components/forms/FormPlus.vue";

const props = defineProps<{
  confirmation: Confirmation | null
}>();
const emit = defineEmits<{
  (e: 'yes', checked: boolean): void
  (e: 'no'): void
}>();

const {t} = useI18n();
const {paragraphize} = useFormatters();

const text: any = computed<string|undefined>(() => {
  if(props.confirmation) {
    const text = props.confirmation.text ? props.confirmation.text : (props.confirmation.textSlug ? t(props.confirmation.textSlug) : undefined);
    if (text) {
      return paragraphize(text) || undefined;
    }
    else {
      return text;
    }
  }
  else {
    return text;
  }
});

const noSlug = computed<string|undefined>(() => {
  if(props.confirmation) {
    return (!props.confirmation.no && !props.confirmation.noSlug) ? 'global.no' : props.confirmation.noSlug;
  } else {
    return undefined
  }
});
const yesSlug = computed<string|undefined>(() => {
  if(props.confirmation) {
    return (!props.confirmation.yes && !props.confirmation.yesSlug) ? 'global.yes' : props.confirmation.yesSlug;
  } else {
    return undefined
  }
});

const checked = ref<boolean>(false);

function no(close: () => void) {
  emit("no");
  close();
}
function yes(close: () => void) {
  if(props.confirmation) {
    emit("yes", props.confirmation.check || props.confirmation.checkSlug ? checked.value : false);
  }
  close();
}

</script>

<template>
  <modal v-if="confirmation" class="ModalConfirmation" :title="confirmation.title" :titleSlug="confirmation.titleSlug">
    <div v-if="text" class="text" v-html="text"/>
    <slot v-else />
    <form-plus v-if="confirmation.check || confirmation.checkSlug">
      <field-checkbox name="check" v-model="checked" :label="confirmation.check" :labelSlug="confirmation.checkSlug"/>
    </form-plus>
    <template #footer="{close}">
      <controls>
        <main-button :label="confirmation.no" :labelSlug="noSlug" type="dimmed" @click="no(close)"/>
        <main-button :label="confirmation.yes" :labelSlug="yesSlug" @click="yes(close)"/>
      </controls>
    </template>
  </modal>
</template>

<style lang="scss">
.ModalConfirmation {
  .ModalBox {
    width: 500px;

    .ModalFooter {
      .Controls {
        margin-bottom: 0;
      }
    }
  }
  .text {
    color: $cmainColor;
    font-size: 15px;
    line-height: 1.3em;

    p {
      margin-top: 12px;

      &:first-child {
        margin-top: 0;
      }

      b {
        font-weight: $fbold;
      }
    }
  }

  .FormPlus {
    margin-top: $gap;
    padding: $gap2;
    background-color: $cmainBackground;
  }

}
</style>