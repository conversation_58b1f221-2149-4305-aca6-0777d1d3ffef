<script setup lang="ts">
import {computed} from "vue";
import IdoubleLeft from "@/components/images/pagination/IdoubleLeft.vue";
import Ileft from "@/components/images/pagination/Ileft.vue";
import Iright from "@/components/images/pagination/Iright.vue";
import IdoubleRight from "@/components/images/pagination/IdoubleRight.vue";
import Iellipsis from "@/components/images/pagination/iellipsis.vue";

const PAGES_AROUND_CURRENT = 2;

const props = defineProps<{
	modelValue: number
	itemsByPage: number
	itemsCount: number
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: number): void
}>();

const isDisplayed = computed<boolean>(() => {
	return (props.itemsByPage > 0) && (props.itemsCount > props.itemsByPage);
});

const lastPage = computed<number>(() => {
	return props.itemsCount > 0 && props.itemsByPage > 0 ? Math.floor((props.itemsCount - 1) / props.itemsByPage) : 0;
});

const pages = computed<number[]>(() => {
	let min = Math.max(0, props.modelValue - PAGES_AROUND_CURRENT);
	let max = Math.min(lastPage.value, props.modelValue + PAGES_AROUND_CURRENT);
	let pages = [];

	for(let i = min; i <= max; ++i) {
		pages.push(i);
	}

	return pages;
});

const hasFirstEllipsis = computed<boolean>(() => {
	return pages.value[0] > 0;
});

const hasLastEllipsis = computed<boolean>(() => {
	return pages.value[pages.value.length - 1] < lastPage.value;
});

function goTo(page: number) {
	if(page >= 0 && page <= lastPage.value && page !== props.modelValue) {
		emit('update:modelValue', page);
	}
}
</script>

<template>
	<div v-if="isDisplayed" class="Pagination">
		<div :class="'arrow first' + (modelValue === 0 ? ' disabled':'')" @click="goTo(0)"><idouble-left/></div>
		<div :class="'arrow previous' + (modelValue === 0 ? ' disabled':'')" @click="goTo(modelValue - 1)"><ileft/></div>
		<div v-if="hasFirstEllipsis" class="ellipsis"><iellipsis/></div>
		<div v-for="page in pages" :key="page" :class="'page' + (modelValue === page ? ' current':'')" @click="goTo(page)">{{page + 1}}</div>
		<div v-if="hasLastEllipsis" class="ellipsis"><iellipsis/></div>
		<div :class="'arrow next' + (modelValue === lastPage ? ' disabled':'')" @click="goTo(modelValue + 1)"><iright/></div>
		<div :class="'arrow last' + (modelValue === lastPage ? ' disabled':'')" @click="goTo(lastPage)"><idouble-right/></div>
	</div>
</template>

<style lang="scss">
.Pagination {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: $gap2;
	padding: 10px;

	.arrow {
		height: 30px;
		width: 30px;
		padding: 4px;
		border: $cboxBorder solid 1px;
		border-radius: $radius;

		.icon {
			width: 100%;
			height: 100%;
		}

		&:hover:not(.disabled) {
			background-color: $cboxBorder;
			cursor: pointer;
		}


		&.disabled {
			color: $cborder;
		}
	}

	.ellipis {
		height: 30px;
		width: 30px;
		padding: 4px;

		.icon {
			width: 100%;
			height: 100%;
		}
	}

	.page {
		padding: 4px;
		border-radius: $radius;
		min-height: 30px;
		min-width: 30px;
		display: flex;
		align-items: center;
		justify-content: center;

		&.current {
			font-weight: $fbold;
			border-color: $cmainColor;
			background-color: $cmainColor;
			color: white;
		}

		&:hover:not(.current) {
			background-color: $cboxBorder;
			cursor: pointer;
		}
	}
}
</style>