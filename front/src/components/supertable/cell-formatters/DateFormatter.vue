<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";
import moment from "moment-timezone";

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	options: any
	/*
	format: string (format moment utilisé) : default = "DD/MM/YYYY"
	 */
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const format = computed<string>(() => {
	if(props.options && props.options.format && typeof props.options.format === "string") {
		return props.options.format;
	}
	else {
		return "DD/MM/YYYY";
	}
});

const dateValue = computed<string>(() => {
	if(props.value !== undefined && props.value !== null) {
		const date = moment(props.value);``
		if(date.isValid()) {
			return date.format(format.value);
		}
	}
	return "<span class='empty'>-</span>";
});

</script>

<template>
	<div class="DateFormatter" v-html="dateValue"/>
</template>

<style lang="scss">
.DateFormatter {
	.empty {
		opacity: 0.5;
	}
}
</style>