<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";
import {Person} from "@shared/crudTypes";
import {capitalize} from "../supertable-helpers";

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	options: any
	/*
		anonymized: boolean (default = false)
	 */
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const anonymized = computed<boolean>(() => {
	return props.options && !!props.options.anonymized;
});

const nameValue = computed<string>(() => {
	const name = userName(props.value, anonymized.value);
	if(name) {
		return name;
	}
	else {
		return "<span class='empty'>-</span>";
	}
});

function userName(user: Person, anonymized?: boolean) {
	if (user) {
		if ((user.firstname && user.firstname.length > 0) || (user.lastname && user.lastname.length > 0)) {
			if (anonymized) {
				return capitalize(user.firstname) + " " + user.lastname.substring(0, 1).toUpperCase() + ".";
			}
			else {
				return capitalize(user.firstname) + " " + capitalize(user.lastname);
			}
		}
	}

	return "";
}
</script>

<template>
	<div class="FullnameFormatter" v-html="nameValue"/>
</template>

<style lang="scss">
.FullnameFormatter {
	.empty {
		opacity: 0.5;
	}
}
</style>