<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";
import get from "lodash.get";

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	//options: any
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const formattedValue = computed<string>(() => {
	const val = props.field ? get(props.item, props.field!.key) : props.value;
	if(val !== undefined && val !== null) {
		return '<span "val">' + val + '</span>';
	}
	return "<span class='empty'>-</span>";
});

</script>

<template>
	<div class="DefaultFormatter" v-html="formattedValue"/>
</template>

<style lang="scss">
.UnityFormatter {
	.empty {
		opacity: 0.5;
	}
}
</style>