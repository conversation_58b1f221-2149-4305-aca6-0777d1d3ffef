<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";

const DEFAULT_COLOR = {
	"false": "#D01212",
	"true": "#52D042"
};

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	options: any
	/*
	true: {label: string, color: string}
	false: {label: string, color: string}
	 */
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const format = computed<string>(() => {
	if(props.options && props.options.format && typeof props.options.format === "string") {
		return props.options.format;
	}
	else {
		return "DD/MM/YYYY";
	}
});

const value = computed<"true"|"false">(() => {
	return hasValue.value && !!props.value ? "true" : "false";
});

const hasValue = computed<boolean>(() => {
	return props.value !== undefined && props.value !== null;
});

const color = computed<string>(() => {
	if(props.options && props.options[value.value] && props.options[value.value].color) {
		return props.options[value.value].color;
	}
	else {
		return DEFAULT_COLOR[value.value]!;
	}
});

const bulletStyle = computed<any>(() => {
	return {backgroundColor: color.value};
});

const text = computed<string>(() => {
	if(props.options && props.options[value.value]) {
		return props.options[value.value].label ? props.options[value.value].label : value.value;
	}
	else {
		return value.value;
	}
});
</script>

<template>
	<div class="BooleanSignal">
		<template v-if="hasValue">
			<div class="bullet" :style="bulletStyle"/>
			<div class="text" v-html="text"/>
		</template>
		<div v-else class="empty">-</div>
	</div>
</template>

<style lang="scss">
.BooleanSignal {
	display: flex;
	align-items: center;
	gap: 10px;

	.bullet {
		width: 8px;
		height: 8px;
		border-radius: 8px;
	}

	.empty {
		opacity: 0.5;
	}
}
</style>