<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	options: any
	/*
		enum: nom de l'énumération
	 */
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const enumName = computed<string|null>(() => {
	return props.options && props.options.enum ? props.options.enum : null;
});

const enumValue = computed<string>(() => {
	if(props.value) {
		if (enumName.value) {
			return t('enum.' + enumName.value + '.' + props.value);
		}
		else {
			return props.value;
		}
	}
	else {
		return "<span class='empty'>-</span>";
	}
});

</script>

<template>
	<div class="EnumFormatter" v-html="enumValue"/>
</template>

<style lang="scss">
.EnumFormatter {
	.empty {
		opacity: 0.5;
	}
}
</style>