<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SuperFormField, TableColumnDefinition} from "@/front-types";
import {computed} from "vue";

const props = defineProps<{
	column?: TableColumnDefinition
	row?: number
	field?: SuperFormField
	item: any
	value: any
	options: any
	/*
	fractionDigits: number (nombre de chiffres après la virgule) : default = 0
	 */
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const fractionDigits = computed<number>(() => {
	if(props.options && props.options.fractionDigits && typeof props.options.fractionDigits === "number") {
		return Math.max(props.options.fractionDigits, 0);
	}
	else {
		return 0;
	}
});

const percentageValue = computed<string>(() => {
	if(props.value !== undefined && props.value !== null) {
		const val = typeof props.value === "number" ? props.value : parseFloat(props.value);
		if(!isNaN(val)) {
			return (val * 100).toFixed(fractionDigits.value).replaceAll(".", ",") + "&nbsp;%";
		}
	}
	return "<span class='empty'>-</span>";
});

</script>

<template>
	<div class="CellFormatterPercent" v-html="percentageValue"/>
</template>

<style lang="scss">
.CellFormatterPercent {
	.empty {
		opacity: 0.5;
	}
}
</style>