<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {
	PaginatedList, SuperTableCompletor,
	SuperTableConfiguration, SuperTableCrudOption,
	SuperTableLoader,
	TableColumnDefinition,
	TablePagination
} from "@/front-types";
import {type Component, computed, ref, watchEffect} from "vue";
import Pagination from "@/components/supertable/Pagination.vue";
import defaultConfiguration from "./SuperTableConfiguration";
import merge from "lodash.merge";
import {DBCollection} from "@shared/enums";
import DefaultFormatter from "@/components/supertable/cell-formatters/DefaultFormatter.vue";

const props = defineProps<{
	columns: TableColumnDefinition[]
	pagination?: TablePagination
	configuration?: SuperTableConfiguration
	interactive?: boolean
	reload?: any
	rows?: any[]
	crud?: SuperTableCrudOption
	loader?: SuperTableLoader
	complete?: SuperTableCompletor
}>();

const emit = defineEmits<{
	(e: 'selected', value: any): void
	(e: 'total', value: number): void
}>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {crudList} = useApi();
const {} = useFormatters();

const loadedItems = ref<any[]>([]);
const loading = ref<boolean>(false);
const error = ref<string | null>(null);
const currentPage = ref<number>(0);
const itemsTotal = ref<number>(0);

// --- CONFIGURATION
const configuration = computed<SuperTableConfiguration>(() => {
	let config: SuperTableConfiguration = {...defaultConfiguration};
	if (props.configuration) {
		merge(config, props.configuration);
	}

	return config;
});

// --- PAGINATION
// -1 = pas de pagination
const itemsByPage = computed<number>(() => {
	const rowsByPage = props.pagination ? Math.max(-1, props.pagination.itemsByPage) : -1;
	if (rowsByPage === 0) {
		return -1;
	}
	else {
		return rowsByPage;
	}
});

const hasPagination = computed<boolean>(() => {
	return itemsByPage.value > 0;
});

const itemsCount = computed<number>(() => {
	if (hasPagination.value) {
		return itemsTotal.value;
	}
	else {
		return items.value.length;
	}
});

// --- FORMAT CELLS

function format(value: any, _type: string) {
	// TODO: formatters de base
	return value;
}

function cellComponent(_item: any, _idx: number, column: TableColumnDefinition): string | Component {
	if (column.formatter) {
		const formatterName = column.formatter.name ? column.formatter.name : column.formatter;
		if (configuration.value.formatters[formatterName]) {
			return configuration.value.formatters[formatterName];
		}
	}

	return DefaultFormatter;
}

function cellComponentOptions(_item: any, _idx: number, column: TableColumnDefinition): any {
	if (column.formatter && column.formatter.name) {
		return column.formatter;
	}
	else {
		return null;
	}
}

watchEffect(async () => {
	if (props.crud || props.loader || props.reload) {
		const options = buildQueryOptions();
		await loadItems(options);
	}
});

function buildQueryOptions(): any {
	let options: any = {};
	if (props.pagination) {
		options.pagination = {
			limit: props.pagination.itemsByPage,
			offset: currentPage.value * props.pagination.itemsByPage
		}
	}
	return options;
}

async function loadItems(options: any) {
	loading.value = true;
	try {
		let result: any[] | PaginatedList<any>;
		if (props.crud) {
			if (typeof props.crud === "string") {
				result = await crudList(props.crud as DBCollection, options);
			}
			else {
				result = await crudList(props.crud.collection as DBCollection, merge({}, options, {
					filters: props.crud.filters,
					sort: props.crud.sort,
					projection: props.crud.projection,
					population: props.crud.population
				}));
			}
		}
		else {
			result = await props.loader!(options);
		}

		loadedItems.value = await completeItems((result as PaginatedList<any>).__list ? (result as PaginatedList<any>).__list : (result as any[]));
		itemsTotal.value = (result as PaginatedList<any>).__pagination ? (result as PaginatedList<any>).__pagination.total : loadedItems.value.length;
		emit('total', itemsTotal.value);
	}
	catch (err: any) {
		console.log("[SUPERTABLE] Unable to load items: ", err);
		error.value = "Unable to load items";	// TODO: rendre paramétrable
	}
	loading.value = false;
}

const items = computed<any[]>(() => {

	if (props.crud || props.loader) {
		return loadedItems.value;
	}
	else if (props.rows) {
		return props.rows;
	}
	else {
		return [];
	}
});

function selectRow(item: any, _idx: number) {
	if (props.interactive) {
		emit('selected', item);
	}
}

async function completeItems(items: any[]): Promise<any[]> {
	if (props.complete) {
		return props.complete(items);
	}
	else {
		return items;
	}
}

function getItemValue(item: any, key: string) {
	const path = key.split(".");

	return _getItemValue(item, path, 0);

	function _getItemValue(item: any, path: string[], index: number) {
		if (item) {
			const key = path[index];
			const value = item[key];
			// console.log("key: ", key, value);
			if (index < path.length - 1) {
				return _getItemValue(value, path, index + 1);
			}
			else {
				return value;
			}
		}
		else {
			return null;
		}
	}
}

</script>

<template>
	<div :class="'SuperTable' + (interactive ? ' interactive':'')">
		<div class="table-header">
			<div v-for="column in columns" :key="column.key" :class="'table-column-header type-' + column.type">
				<slot :name="'header.' + column.key" :column="column">{{ t((column.labelSlug)) }}</slot>
			</div>
		</div>
		<div class="table-body">
			<div class="table-row" v-if="items.length === 0">
				<div v-for="column in columns" :key="column.key" :class="'table-cell type-' + column.type">
					<slot :name="'cell.' + column.key" :column="column">
						-
					</slot>
				</div>
			</div>
			<div class="table-row" v-else v-for="(item, idx) in items" :key="'item-' + idx"
				 @click="selectRow(item, idx)">
				<div v-for="column in columns" :key="column.key" :class="'table-cell type-' + column.type">
					<slot :name="'cell.' + column.key" :column="column" :index="idx" :item="item">
						<component :is="cellComponent(item, idx, column)" :column="column" :item="item" :row="idx"
								   :value="getItemValue(item, column.key)"
								   :options="cellComponentOptions(item, idx, column)">

							{{ format(item[column.key], column.type) ? format(item[column.key], column.type) : "-" }}
						</component>
					</slot>
				</div>
			</div>
		</div>
		<div v-if="hasPagination" class="table-footer">
			<Pagination :itemsByPage="itemsByPage" :itemsCount="itemsCount" v-model="currentPage"/>
		</div>
	</div>
</template>

<style lang="scss">
.SuperTable {
	--st-even-row: #F5F7FA;
	--st-hover-row: #e3e6fc;

	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	border: var(--st-border) solid 1px;
	border-radius: 8px;

	.table-header {
		display: flex;
		border-bottom: solid 1px #E6EFF5;

		.table-column-header {
			flex: 1 1 0;
			padding: 9px 12px;
			font-size: $fsregular;
			font-weight: $fbold;
			color: $cbrightBlue;
		}
	}

	.table-body {
		flex: 1 1 0;
		overflow-y: auto;

		.table-row {
			display: flex;
			border-radius: 8px;

			.table-cell {
				flex: 1 1 0;
				padding: $gap2;
				font-size: $fsregular;
				font-weight: $fregular;
				color: $cmainColor;
			}

			&:nth-child(even) {
				background-color: var(--st-even-row);
			}
		}
	}

	.table-footer {
		border-top: var(--st-border) solid 1px;
	}

	&.interactive {
		.table-body {
			.table-row:hover {
				background-color: var(--st-hover-row);
				cursor: pointer;
			}
		}
	}
}
</style>
