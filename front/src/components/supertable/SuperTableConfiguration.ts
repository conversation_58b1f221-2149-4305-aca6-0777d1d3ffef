import type {SuperTableConfiguration} from "@/front-types";
import CellFormatterPercent from "@/components/supertable/cell-formatters/CellFormatterPercent.vue";
import DateFormatter from "@/components/supertable/cell-formatters/DateFormatter.vue";
import BooleanSignal from "@/components/supertable/cell-formatters/BooleanSignal.vue";
import FullnameFormatter from "@/components/supertable/cell-formatters/FullnameFormatter.vue";
import UnityFormatter from "@/components/supertable/cell-formatters/UnityFormatter.vue";
import EnumFormatter from "@/components/supertable/cell-formatters/EnumFormatter.vue";
import NumberFormatter from "@/components/supertable/cell-formatters/NumberFormatter.vue";

const configuration: SuperTableConfiguration = {
	formatters: {
		bsignal: BooleanSignal,
		date: DateFormatter,
		enum: EnumFormatter,
		fullname: FullnameFormatter,
		number: NumberFormatter,
		percent: CellFormatterPercent,
		unity: <PERSON>Formatter
	}
}

export default configuration;