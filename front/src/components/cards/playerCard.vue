<script setup lang="ts">
import { IPlayerDB } from '@shared/crudTypes';
import { PlayerStatus } from '@shared/enums';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { calculateAge } from '@/common/helpers/computations';
import IaddPlayer from '@/components/images/IaddPlayer.vue';
import MainButton from '@/components/ui/MainButton.vue';
import SmallInfo from '@/components/ui/SmallInfo.vue';

const props = defineProps<{
  player: IPlayerDB;
  addMode?: Boolean;
}>();
const emit = defineEmits<{
  (e: 'addPlayer'): void;
}>();

const { t } = useI18n();
const imageUrl = ref<string>(props.player.external_photo);
const display = ref<boolean>(false);

watch(
  () => props.player.external_photo,
  (newValue, _oldValue) => {
    imageUrl.value = newValue;
  },
);

const invited = computed(() => {
  return props.player.status === PlayerStatus.invited;
});

function handleAddPlayerClick() {
  emit('addPlayer');
}

function handleImageError(player: IPlayerDB) {
  display.value = true;
  imageUrl.value =
    player.external_gender_name === 'M'
      ? 'http://localhost:28100/src/assets/men.png'
      : 'http://localhost:28100/src/assets/women.png';
}

const positions = computed(() => {
  let str = '';

  if (props.player.external_position1_name) {
    str += props.player.external_position1_name;
  }
  if (props.player.external_position2_name) {
    if (str.length > 0) {
      str += '<em> / </em>';
    }
    str += '<i>' + props.player.external_position2_name + '</i>';
  }

  if (str.length > 0) {
    return str;
  } else {
    return null;
  }
});
</script>

<template>
  <div class="playerCard" :class="invited ? 'invited' : ''">
    <div class="image" :style="display ? { background: 'none' } : {}">
      <img
        alt="player"
        :src="imageUrl"
        @error="handleImageError(player)"
        @load="display = true"
        :style="display ? {} : { display: 'none' }"
      />
    </div>
    <div class="playerCardContent">
      <p class="name">{{ player.external_firstname + ' ' + player.external_lastname }}</p>
      <div class="smallInfos">
        <small-info>
          <template v-slot:title>{{ t('player.card.team') }}</template>
          {{ player.external_club_team_name }}
        </small-info>
        <!--				<small-info>-->
        <!--					<template v-slot:title>{{ t('player.card.country') }}</template>-->
        <!--					{{ player.external_country1_name }}-->
        <!--				</small-info>-->
        <small-info>
          <template v-slot:title>{{ t('player.card.age') }}</template>
          {{ player?.external_birthday ? calculateAge(player!.external_birthday) : '-' }}
        </small-info>
        <small-info v-if="player.external_height">
          <template v-slot:title>{{ t('player.card.height') }}</template>
          {{ player.external_height }}
        </small-info>
        <small-info v-if="positions">
          <template v-slot:title>{{ t('player.card.position') }}</template>
          <span v-html="positions" />
        </small-info>
      </div>
      <template v-if="addMode && !invited">
        <div class="spacer" />
        <main-button @click="handleAddPlayerClick">
          <IaddPlayer />
          {{ t('global.add') }}
        </main-button>
      </template>
      <template v-if="invited">
        <div class="spacer" />
        <div class="invitedText" v-if="invited">{{ t('player.card.invitedText') }}</div>
      </template>
    </div>
  </div>
</template>

<style lang="scss">
.invited {
  opacity: 30%;

  .invitedText {
    text-align: center;
    color: $cgrayBlue;
    font-size: 11px;
    font-weight: $fbold;
    padding: 14px;
  }
}

.playerCard {
  background-color: $cwhite;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 4px 3px 0px rgba($cgrayBlue, 0.3);

  .image {
    width: 100%;
    border-radius: 20px;
    aspect-ratio: 1;
    border: 2px solid $cborderGray;
    background-image: url('http://localhost:28100/src/assets/men.png');
    background-size: cover;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .playerCardContent {
    flex: 1 1 0;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .name {
      color: $cbrightBlue;
      font-size: 16px;
      font-weight: $fbold;
    }

    .smallInfos {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .MainButton {
      width: fit-content;
      margin: auto;
      padding: 9px 15px;
      border-radius: 10px;
    }
  }

  .spacer {
    flex: 1 1 0;
  }
}
</style>
