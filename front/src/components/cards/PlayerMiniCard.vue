<script setup lang="ts">
import { IPlayerDB } from '@shared/crudTypes';
import { Gender, PlayerStatus } from '@shared/enums';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { calculateAge } from '@/common/helpers/computations';
import IaddPlayer from '@/components/images/IaddPlayer.vue';
import MainButton from '@/components/ui/MainButton.vue';
import SmallInfo from '@/components/ui/SmallInfo.vue';

const props = defineProps<{
  player: IPlayerDB;
}>();
// const emit = defineEmits<{
// 	(e: 'addPlayer'): void
// }>();

const { t } = useI18n();
const imageUrl = ref<string>(props.player.external_photo);
const display = ref<boolean>(false);

watch(
  () => props.player.external_photo,
  (newValue, _oldValue) => {
    imageUrl.value = newValue;
  },
);

function handleImageError(player: IPlayerDB) {
  display.value = true;
  imageUrl.value =
    player.external_gender_name === Gender.M
      ? 'http://localhost:28100/src/assets/men.png'
      : 'http://localhost:28100/src/assets/women.png';
}
</script>

<template>
  <div class="PlayerMiniCard">
    <div class="image" :style="display ? { background: 'none' } : {}">
      <img
        alt="player"
        :src="imageUrl"
        @error="handleImageError(player)"
        @load="display = true"
        :style="display ? {} : { display: 'none' }"
      />
    </div>
    <div class="playerCardContent">
      <p class="name">{{ player.external_firstname + ' ' + player.external_lastname }}</p>
      <div class="smallInfos">
        <small-info class="team">
          {{ player.external_club_team_name }}
        </small-info>
        <small-info class="lastSession" v-if="player.lastSeasonYear">
          <template v-slot:title>{{ t('player.card.lastSeasonYear') }}</template>
          {{ player.lastSeasonYear }}
        </small-info>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.PlayerMiniCard {
  background-color: $cwhite;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  flex-direction: column;

  img,
  .image {
    width: 100%;
    border-radius: 20px;
    aspect-ratio: 1;
  }

  .image {
    background-image: url('http://localhost:28100/src/assets/men.png');
  }

  .playerCardContent {
    flex: 1 1 0;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .name {
      color: $cbrightBlue;
      font-size: 14px;
      font-weight: $fbold;
    }

    .smallInfos {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .smallInfo {
        .smallInfoContent {
          font-size: 12px;
        }
      }
    }
  }

  .spacer {
    flex: 1 1 0;
  }
}
</style>
