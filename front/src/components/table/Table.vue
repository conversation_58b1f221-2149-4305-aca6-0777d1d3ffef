<script setup>
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { AgGridVue } from 'ag-grid-vue3';
import { ChevronUp } from 'lucide-vue-next';
import { computed, ref } from 'vue';

import { Container, Typography } from '../ui';

ModuleRegistry.registerModules([AllCommunityModule]);

const props = defineProps({
	columnDefs: { type: Array, required: true },
	rowData: { type: Array, required: true },
	components: { type: Object, default: () => ({}) },
	disableShowMore: { type: Boolean, default: false },
});

const visibleCount = ref(5);
const visibleRows = computed(() =>
	!props.disableShowMore ? props.rowData.slice(0, visibleCount.value) : props.rowData,
);

const defaultColDef = { flex: 1 };

const emit = defineEmits(['row-clicked']);
const onRowClicked = (event) => {
	emit('row-clicked', event.data?.hoverData?._id);
};

const showMore = () => {
	visibleCount.value += 5;
};
</script>

<template>
	<div>
		<ag-grid-vue
			:columnDefs="props.columnDefs"
			:rowData="visibleRows"
			:defaultColDef="defaultColDef"
			:pagination="false"
			:domLayout="'autoHeight'"
			:rowHeight="50"
			:components="props.components"
			:tooltipShowDelay="200"
			@rowClicked="onRowClicked"
		/>
		<Container v-if="!disableShowMore && visibleCount < props.rowData.length" class="showMore" @click="showMore">
			<Typography>{{ $t('common.table.showMore') }}</Typography>
			<ChevronUp :size="18" />
		</Container>
	</div>
</template>

<style lang="scss">
.ag-row,
.ag-header,
.ag-root-wrapper {
	border: none;
}

.ag-header {
	background-color: $white;
	font-weight: 500;
	color: $charcoal;
}

.ag-header-cell {
	z-index: 0;
}

.ag-row-odd {
	background-color: $white;
}

.ag-row-even {
	background-color: $white-off;
}

.ag-cell {
	line-height: 50px;
}

.ag-row:hover {
	cursor: pointer;
}

.showMore {
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
	padding: 10px 20px;
	font-weight: 500;
	cursor: pointer;
	background-color: $white;
	line-height: 50px;
	gap: 8px;

	> * {
		color: $charcoal-light;
	}
}
</style>
