<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import {IPlayerDB, IShortlistDB} from "@shared/types";
import FormPlus from "@/components/forms/FormPlus.vue";
import {computed, ref} from "vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";
import {useApi} from "@/composition/api";
import LoadingContext from "@/components/ui/LoadingContext.vue";
import FieldSelect from "@/components/forms/FieldSelect.vue";
import {SelectOption} from "@/front-types";
import ModalAddShortlist from "@/components/shortlist/ModalAddShortlist.vue";
import {useValidators} from "@/composition/validators";

const {t} = useI18n()

const props = defineProps<{
	player: IPlayerDB
}>();
const emit = defineEmits<{
	(e: 'added', shortlist: IShortlistDB): void
}>();

const {loadShortlists, createShortlist, addPlayerToShortlist} = useApi();
const {serverErrorsToLocalErrors} = useValidators();

const shortlists = ref<IShortlistDB[]>([]);
const shortlistId = ref<string|null>(null);
const shortlistErrors = ref<any>(null);
const errors = ref<any>(null);

async function getShortlists() {
	const result = await loadShortlists();
	shortlists.value = [
		...result.mine,
		...result.shared
	];
}

const shortlistOptions = computed<SelectOption<string>[]>(() => {
	return shortlists.value.map(shortlist => {
		return {label: shortlist.name, value: shortlist._id};
	});
});

const addShortlistModalShown = ref<boolean>(false);

function showAddShortlistModal() {
	addShortlistModalShown.value = true;
}

async function addingPlayerToShortlist(close: () => void) {
	if(shortlistId.value) {
		errors.value = null;
		try {
			const updatedShortlist = await addPlayerToShortlist(shortlistId.value, props.player._id);
			close();
			emit("added", updatedShortlist);
		}
		catch(err: any) {
			console.log("Error creating shortlist: ", err);
			errors.value = serverErrorsToLocalErrors(err);
		}
	}
	else {
		errors.value = {
			shortlist: {id: "empty"}
		}
	}
}

async function createNewShortlist(name: string) {
	shortlistErrors.value = null;
	try {
		const newShortlist = await createShortlist({name});
		shortlists.value.push(newShortlist);
		shortlistId.value = newShortlist._id;
		addShortlistModalShown.value = false;
	}
	catch(err: any) {
		console.log("Error creating shortlist: ", err);
		shortlistErrors.value = serverErrorsToLocalErrors(err);
	}
}

</script>

<template>
	<modal class="ModalAddPlayerToShortlist" :close-button="true" title-slug="shortlist.addPlayer.title">
		<loading-context :loader="getShortlists">
			<form-plus :errors="errors" error-prefix="addPlayer">
				<field-select name="shortlist" v-model="shortlistId" :options="shortlistOptions" label-slug="shortlist.addPlayer.shortlistId"/>
				<div class="or">{{t('shortlist.addPlayer.or')}}</div>
				<main-button type="main" label-slug="shortlist.addPlayer.create" @click="showAddShortlistModal"/>
			</form-plus>
		</loading-context>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button @click="addingPlayerToShortlist(close)">{{ t('shortlist.addPlayer.submit') }}</main-button>
			</controls>
		</template>
		<modal-add-shortlist errors="shortlistErrors" v-if="addShortlistModalShown" @close="addShortlistModalShown = false" @create="createNewShortlist"/>
	</modal>
</template>

<style lang="scss">
.ModalAddPlayerToShortlist {
	.ModalBox {
		width: 410px;

		.or {
			text-align: center;
			color: $cbrightBlue;
			font-size: 15px;
			font-weight: 500;
			line-height: 1.3em;
		}
	}
}
</style>