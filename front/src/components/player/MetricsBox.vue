<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {ComputedMetric} from "@shared/types";
import Box from "@/components/ui/Box.vue";
import {computed} from "vue";

const props = defineProps<{
	title: string
	metrics: ComputedMetric[]
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

function getValue(metric: ComputedMetric) : string {
	if(metric.value !== null && metric.value !== undefined) {
		if (metric.isPercentage) {
			return (metric.value * 100).toFixed(1) + "%";
		}
		else {
			return metric.value.toFixed(1);
		}
	}
	else {
		return "-";
	}
}

</script>

<template>
	<box class="MetricsBox" :title="title">
		<div class="metric" v-for="metric in metrics">
			<div class="label">{{metric.label}}</div>
			<div class="value">{{getValue(metric)}}</div>
		</div>
	</box>
</template>

<style lang="scss">
.MetricsBox {
	.content {
		display: flex;
		flex-direction: column;
		gap: 8px;

		.metric {
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 8px;

			font-size: 14px;

			.label {
				font-weight: $fregular;
			}

			.value {
				font-weight: $fmedium;
			}
		}
	}
}
</style>