<script setup lang="ts">
import { Gender } from '@shared/enums';
import { IPlayerDB } from '@shared/types';
import { ref, watch } from 'vue';

import { useApi } from '@/composition/api';

const props = defineProps<{
	player: IPlayerDB;
}>();

const {} = useApi();

const imageUrl = ref<string>(props.player.external_photo);
const display = ref<boolean>(false);

watch(
	() => props.player.external_photo,
	(newValue: string, _oldValue: string) => {
		imageUrl.value = newValue;
	},
);

function handleImageError(player: IPlayerDB) {
	display.value = true;
	imageUrl.value = player.external_gender_name === Gender.M ? '/images/player/men.png' : '/images/player/women.png';
}
</script>

<template>
	<div class="PlayerImage" :style="display ? { background: 'none' } : {}">
		<img
			alt="player"
			:src="imageUrl"
			@error="handleImageError(player)"
			@load="display = true"
			:style="display ? {} : { display: 'none' }"
		/>
	</div>
</template>

<style lang="scss">
.PlayerImage {
	border-radius: 20px;
	aspect-ratio: 1;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 20px;
	}
}
</style>
