<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";

const props = defineProps<{
	value: number
	label: string
	color: string
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

</script>

<template>
	<div :class="'RoundStat ' + color">
		<div class="round">
			<div class="icon-container"><slot/></div>
			<div class="value">{{value}}</div>
		</div>
		<div class="label">{{label}}</div>
	</div>
</template>

<style lang="scss">
.RoundStat {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10px;

	.round {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 10px;
		border-radius: 1000px;
		border: 4px solid $cbrightBlue;
		color: $cbrightBlue;
		width: 100%;
		height: auto;
		aspect-ratio: 1;

		.icon-container {
			width: 30px;
			height: 30px;
			.icon {
				width: 100%;
				height: 100%;
			}
		}

		.value {
			font-size: 24px;
			font-weight: $fbold;
		}
	}

	.label {
		font-size: 14px;
		font-weight: $fregular;
		color: $cTextSecondary;
		text-align: center;
	}

	&.red {
		.round {
			border-color: $cred;
			color: $cred;
		}
	}

	&.green {
		.round {
			border-color: $cgreen;
			color: $cgreen;
		}
	}

	&.blue {
		.round {
			border-color: $cbrightBlue;
			color: $cbrightBlue;
		}
	}

	&.yellow {
		.round {
			border-color: $cyellow;
			color: $cyellow;
		}
	}

	&.purple {
		.round {
			border-color: $cviolet;
			color: $cviolet;
		}
	}
}
</style>