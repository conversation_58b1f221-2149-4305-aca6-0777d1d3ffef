<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import Ilabel from "@/components/images/Ilabel.vue";

// const props = defineProps<{
//     
// }>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

</script>

<template>
	<div class="PlayerBadge">
		<ilabel/><span><slot/></span>
	</div>
</template>

<style lang="scss">
.PlayerBadge {
	background-color: rgba($cbrightBlue, 0.1);
	color: $cbrightBlue;
	border: rgba($cbrightBlue, 0.1) solid 1px;
	border-radius: 1000px;
	padding: 4px 12px;
	display: flex;
	align-items: center;
	gap: 4px;
	font-size: 14px;
	font-weight: $fregular;

	.icon {
		width: 1rem;
		height: 1rem;
	}
}
</style>