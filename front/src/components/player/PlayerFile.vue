<script setup lang="ts">
import { IPlayerDB, IShortlistDB } from '@shared/types';
import { computed, ref } from 'vue';
import { toast } from 'vue3-toastify';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { calculateAge } from '@/common/helpers/computations';
import Ibrain from '@/components/images/Ibrain.vue';
import Iconplayer from '@/components/images/Iconplayer.vue';
import Iflash from '@/components/images/Iflash.vue';
import Ishield from '@/components/images/Ishield.vue';
import Itarget from '@/components/images/Itarget.vue';
import Ileft from '@/components/images/pagination/Ileft.vue';
import MetricsBox from '@/components/player/MetricsBox.vue';
import ModalAddPlayerToShortlist from '@/components/player/ModalAddPlayerToShortlist.vue';
import PlayerBadge from '@/components/player/PlayerBadge.vue';
import PlayerImage from '@/components/player/PlayerImage.vue';
import RoundStat from '@/components/player/RoundStat.vue';
import SuperTable from '@/components/supertable/SuperTable.vue';
import BlueTitle from '@/components/ui/BlueTitle.vue';
import Box from '@/components/ui/Box.vue';
import MainButton from '@/components/ui/MainButton.vue';
import SmallInfo from '@/components/ui/SmallInfo.vue';
import UnderlineTitle from '@/components/ui/UnderlineTitle.vue';
import { useAnalytics } from '@/composition/analytics';
import { useApi } from '@/composition/api';
import { useFormatters } from '@/composition/formatters';
import { useAuth } from '@/composition/snark-auth';
import { Routes } from '@/enums';
import { PaginatedList, TableColumnDefinition } from '@/front-types';

const props = defineProps<{
  player: IPlayerDB;
}>();
const emit = defineEmits<{
  (e: 'close'): void;
}>();

const router = useRouter();
const currentRoute = useRoute();
const { t } = useI18n();
const { sendEventAnalytics } = useAnalytics();
const { currentUser, isAuth } = useAuth();
const { getPlayerStatByMatch, getPlayerStatBySeason } = useApi();
const {} = useFormatters();

function formatHeight(heightCm: number) {
  const meters = Math.floor(heightCm / 100);
  const centimeters = heightCm % 100;
  return `${meters}m${centimeters.toString().padStart(2, '0')}`;
}

function formatNull(info: any): any {
  if (info) {
    if (typeof info === 'number') {
      return info.toFixed(2);
    } else {
      return info;
    }
  } else {
    return '-';
  }
}

const positions = computed(() => {
  let str = '';

  if (props.player.external_position1_name) {
    str += props.player.external_position1_name;
  }
  if (props.player.external_position2_name) {
    if (str.length > 0) {
      str += '<em> / </em>';
    }
    str += '<i>' + props.player.external_position2_name + '</i>';
  }

  if (str.length > 0) {
    return str;
  } else {
    return null;
  }
});

const bigStats = [
  {
    label: 'Scoring',
    color: 'red',
    value: 76,
    icon: Itarget,
  },
  {
    label: 'Playmaking',
    color: 'green',
    value: 36,
    icon: Ibrain,
  },
  {
    label: 'Defense',
    color: 'yellow',
    value: 55,
    icon: Ishield,
  },
  {
    label: 'Impact',
    color: 'purple',
    value: 96,
    icon: Iflash,
  },
];

const playerMetrics = [
  {
    title: 'Scoring Metrics',
    metrics: [
      {
        label: 'True Shooting %',
        value: 0.515,
        isPercentage: true,
      },
      {
        label: 'Points Per Possession',
        value: 1,
      },
    ],
  },
  {
    title: 'Playmaking Metrics',
    metrics: [
      {
        label: 'Assist/Turnover',
        value: 1.5,
      },
      {
        label: 'Points Created',
        value: 9.3,
      },
      {
        label: 'Assists Per Game',
        value: 3.9,
      },
    ],
  },
  {
    title: 'Defense Metrics',
    metrics: [
      {
        label: 'Defensive Rating',
        value: 70.8,
      },
      {
        label: 'Opponent FG%',
        value: 0,
        isPercentage: true,
      },
      {
        label: 'Blocks Per Game',
        value: 0.2,
      },
      {
        label: 'Steals Per Game',
        value: 0.8,
      },
      {
        label: 'Contested Shots',
        value: 0,
      },
    ],
  },
  {
    title: 'Efficiency Metrics',
    metrics: [
      {
        label: 'Net Rating',
        value: 29.2,
      },
      {
        label: 'Offensive Rating',
        value: 100,
      },
      {
        label: 'Defensive Rating',
        value: 70.8,
      },
      {
        label: 'Usage Rate',
        value: 0.159,
        isPercentage: true,
      },
      {
        label: 'Fouls Drawn',
        value: 5.6,
      },
    ],
  },
];

const matchTableColumns: TableColumnDefinition[] = [
  {
    key: 'Season',
    type: 'string',
    labelSlug: 'player.stat.season.label',
  },
  {
    key: 'Match',
    type: 'string',
    labelSlug: 'player.stat.matchName.label',
  },
  {
    key: 'Points',
    type: 'string',
    labelSlug: 'player.stat.points.label',
  },
  {
    key: 'Rebounds',
    type: 'string',
    labelSlug: 'player.stat.rebounds.label',
  },
  {
    key: 'Assists',
    type: 'string',
    labelSlug: 'player.stat.assists.label',
  },
  {
    key: 'Field goals, %',
    type: 'string',
    labelSlug: 'player.stat.fieldGoalsPercentage.label',
  },
  {
    key: '2-pt field goals, %',
    type: 'string',
    labelSlug: 'player.stat.twoPointFieldGoalsPercentage.label',
  },
  {
    key: '3-pt field goals, %',
    type: 'string',
    labelSlug: 'player.stat.threePointFieldGoalsPercentage.label',
  },
  {
    key: 'Free throws, %',
    type: 'string',
    labelSlug: 'player.stat.freeThrowsPercentage.label',
  },
  {
    key: 'Offensive rebounds',
    type: 'string',
    labelSlug: 'player.stat.offensiveRebounds.label',
  },
  {
    key: 'Defensive rebounds',
    type: 'string',
    labelSlug: 'player.stat.defensiveRebounds.label',
  },
  {
    key: 'Steals',
    type: 'string',
    labelSlug: 'player.stat.steal.label',
  },
  {
    key: 'Turnovers',
    type: 'string',
    labelSlug: 'player.stat.turnovers.label',
  },
  {
    key: 'Blocks',
    type: 'string',
    labelSlug: 'player.stat.blocks.label',
  },
  {
    key: 'Fouls',
    type: 'string',
    labelSlug: 'player.stat.fouls.label',
  },
];

const seasonTableColumns: TableColumnDefinition[] = [
  {
    key: 'seasonName',
    type: 'string',
    labelSlug: 'player.stat.season.label',
  },
  {
    key: 'Points.avg',
    type: 'string',
    labelSlug: 'player.stat.points.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Rebounds.avg',
    type: 'string',
    labelSlug: 'player.stat.rebounds.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Assists.avg',
    type: 'string',
    labelSlug: 'player.stat.assists.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Field goals, %.avg',
    type: 'string',
    labelSlug: 'player.stat.fieldGoalsPercentage.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: '2-pt field goals, %.avg',
    type: 'string',
    labelSlug: 'player.stat.twoPointFieldGoalsPercentage.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: '3-pt field goals, %.avg',
    type: 'string',
    labelSlug: 'player.stat.threePointFieldGoalsPercentage.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Free throws, %.avg',
    type: 'string',
    labelSlug: 'player.stat.freeThrowsPercentage.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Offensive rebounds.avg',
    type: 'string',
    labelSlug: 'player.stat.offensiveRebounds.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Defensive rebounds.avg',
    type: 'string',
    labelSlug: 'player.stat.defensiveRebounds.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Steals.avg',
    type: 'string',
    labelSlug: 'player.stat.steal.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Turnovers.avg',
    type: 'string',
    labelSlug: 'player.stat.turnovers.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Blocks.avg',
    type: 'string',
    labelSlug: 'player.stat.blocks.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
  {
    key: 'Fouls.avg',
    type: 'string',
    labelSlug: 'player.stat.fouls.label',
    formatter: {
      name: 'number',
      digits: 2,
    },
  },
];

async function loadPlayerStatByMatch(options: {
  pagination?: any;
  filters?: any;
  sort?: any;
  projection?: any;
}): Promise<any[] | PaginatedList<any>> {
  if (props.player._id) {
    return getPlayerStatByMatch(props.player._id, options.pagination.limit, options.pagination.offset);
  } else {
    return [];
  }
}

async function loadPlayerStatBySeason(options: {
  pagination?: any;
  filters?: any;
  sort?: any;
  projection?: any;
}): Promise<any[] | PaginatedList<any>> {
  if (props.player._id) {
    return getPlayerStatBySeason(props.player._id, options.pagination.limit, options.pagination.offset);
  } else {
    return [];
  }
}

const shortlistAddModalShown = ref(false);

function showShortlistAddModal() {
  shortlistAddModalShown.value = true;
}

function playerAddedToShortlist(shortlist: IShortlistDB) {
  toast.success(t('player.actions.toShortlist.success', [shortlist.name]));
}

</script>

<template>
  <div class="PlayerFile">
    <div class="player-header">
      <div class="left">
        <Ileft class="backButton" @click="emit('close')" />
        <BlueTitle>
          <Iconplayer />
          {{ player.external_firstname + ' ' + player.external_lastname }}
        </BlueTitle>
      </div>
      <div class="actions">
        <MainButton type="main" @click="showShortlistAddModal" label-slug="player.actions.toShortlist.cta" />
        <MainButton
          v-if="currentUser?.features?.playerSimilarity"
          class="funky"
          :to="{ name: Routes.aiPlayerSimilarity, params: { playerId: player._id } }"
          label-slug="player.actions.similarity"
        />
      </div>
    </div>
    <div class="player-detail">
      <box class="mainInfo">
        <div class="player-picture">
          <player-image :player="player" />
          <div class="badges">
            <player-badge>Scoring Virtuoso</player-badge>
            <player-badge>Game Changer</player-badge>
          </div>
        </div>
        <div class="infoContainer">
          <underline-title>{{ t('player.focus.personalData.title') }}</underline-title>
          <small-info>
            <template v-slot:title>{{ t('player.focus.personalData.country') }}</template>
            {{ formatNull(player!.external_country1_name) }}
          </small-info>
          <small-info>
            <template v-slot:title>{{ t('player.focus.personalData.age') }}</template>
            {{ player?.external_birthday ? calculateAge(player!.external_birthday) : '-' }}
          </small-info>
          <small-info>
            <template v-slot:title>{{ t('player.focus.personalData.birth') }}</template>
            {{ formatNull(player!.external_birthday) }}
          </small-info>
          <small-info>
            <template v-slot:title>{{ t('player.focus.personalData.height') }}</template>
            {{ player!.external_height ? formatHeight(player!.external_height) : '-' }}
          </small-info>
          <small-info>
            <template v-slot:title>{{ t('player.focus.game.team') }}</template>
            {{ formatNull(player!.external_club_team_name) }}
          </small-info>
          <small-info>
            <template v-slot:title>{{ t('player.focus.game.position') }}</template>
            <span v-html="positions" />
          </small-info>
        </div>
        <div class="analysis">
          <underline-title>{{ t('player.focus.analysis.title') }}</underline-title>
          <div class="key-roles">Role Player • Game Changer</div>
          <div class="sections">
            <section class="strengths">
              <h3>Strengths</h3>
              <ul class="points">
                <li class="point">Significant two-way impact through versatile play on both ends</li>
              </ul>
            </section>
            <section class="weaknesses">
              <h3>Areas for development</h3>
              <ul class="points">
                <li class="point">Can improve catch-and-shoot efficiency from three-point range</li>
                <li class="point">Should develop more consistent mid-range game off movement</li>
              </ul>
            </section>
          </div>
        </div>
      </box>
      <div class="big-stats">
        <round-stat
          v-for="stat in bigStats"
          :key="stat.label"
          :value="stat.value"
          :label="stat.label"
          :color="stat.color"
        >
          <component :is="stat.icon" />
        </round-stat>
      </div>
      <div class="metrics">
        <metrics-box v-for="metric in playerMetrics" :title="metric.title" :metrics="metric.metrics" />
      </div>

      <Box class="additionalInfo">
        <template #title>{{ t('player.sections.seasons') }}</template>
        <SuperTable :columns="seasonTableColumns" :loader="loadPlayerStatBySeason" :pagination="{ itemsByPage: 10 }" />
      </Box>
      <Box class="additionalInfo">
        <template #title>{{ t('player.sections.matches') }}</template>
        <SuperTable :columns="matchTableColumns" :loader="loadPlayerStatByMatch" :pagination="{ itemsByPage: 10 }" />
      </Box>
    </div>
    <modal-add-player-to-shortlist
      :player="player"
      @added="playerAddedToShortlist"
      v-if="shortlistAddModalShown"
      @close="shortlistAddModalShown = false"
    />
  </div>
</template>

<style lang="scss">
.PlayerFile {
  width: 80vw;
  height: 100%;
  padding: $gap;
  background-color: $cbackgroundGray;
  display: flex;
  flex-direction: column;
  gap: 30px;

  .player-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      gap: 15px;
      align-items: center;

      .backButton {
        width: 40px;
        height: 40px;
        cursor: pointer;
        text-decoration: none;
        color: $cbrightBlue;
      }

      .blueTitle {
        display: flex;
        gap: 15px;
        align-items: center;
        font-style: italic;
      }

      .status {
        padding: 6px 9px;
        background-color: $cdarkBlue;
        border-radius: 5px;
        color: $cwhite;
      }
    }

    .actions {
      display: flex;
      gap: 30px;
    }
  }

  .player-detail {
    flex: 1 1 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 30px;

    .mainInfo {
      .content {
        display: flex;
        gap: 30px;

        .underlineTitle {
          margin-bottom: 15px;
        }

        .player-picture {
          flex: 1 1 25%;

          img {
            border: 2px solid $cborderGray;
            border-radius: 20px;
            overflow: hidden;
            aspect-ratio: 1;
            width: 100%;
            height: auto;
          }

          .badges {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
          }
        }

        .infoContainer {
          flex: 1 1 25%;
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .analysis {
          flex: 1 1 50%;
          display: flex;
          flex-direction: column;
          gap: 15px;

          .key-roles {
            //font-style: italic;
            font-size: 16px;
            color: $cblack;
            font-weight: $fbold;
            margin-bottom: 15px;
          }

          .sections {
            display: flex;
            flex-direction: column;
            gap: 30px;

            section {
              h3 {
                font-weight: $fbold;
                margin-bottom: 10px;
                font-size: $fsmedium;
                color: $cbrightBlue;
              }

              .points {
                list-style: none;
                padding-left: 0;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .point {
                  display: flex;
                  flex-direction: row;
                  align-items: flex-start;
                  gap: 10px;

                  &::before {
                    display: block;
                    content: '';
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: $cbrightBlue;
                    margin-top: 3px;
                  }
                }
              }

              &.strengths {
                .point {
                  &::before {
                    background-color: $cgreen;
                  }
                }
              }

              &.weaknesses {
                .point {
                  &::before {
                    background-color: $cyellow;
                  }
                }
              }
            }
          }
        }
      }
    }

    .big-stats {
      display: flex;
      justify-content: space-around;
      gap: 30px;
      margin: 30px 0;

      .RoundStat {
        width: 100px;
      }
    }

    .metrics {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(40%, 1fr));
      gap: 30px;
    }

    .additionalInfo {
      margin-bottom: 30px;
      overflow-y: clip;

      .SuperTable {
        .table-body {
          overflow-y: initial;
        }
      }
    }
  }
}
</style>
