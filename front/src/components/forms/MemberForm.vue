<script setup lang="ts">
import { IUser, IUserDB } from '@shared/crudTypes';
import { OrganizationType, Role, UserStatus } from '@shared/enums';
import { ref } from 'vue';
import { toast } from 'vue3-toastify';
import { useI18n } from 'vue-i18n';

import FieldInput from '@/components/forms/FieldInput.vue';
import FieldSelect from '@/components/forms/FieldSelect.vue';
import FormPlus from '@/components/forms/FormPlus.vue';
import MainButton from '@/components/ui/MainButton.vue';
import { useApi } from '@/composition/api';
import { useFormatters } from '@/composition/formatters';
import { useAuth } from '@/composition/snark-auth';
import { useValidators } from '@/composition/validators';

const { currentUser } = useAuth();
const { inviteUser, updateUser } = useApi();
const { validateUserData, serverErrorsToLocalErrors } = useValidators();
const { getSelectOptions } = useFormatters();

const errors = ref<any>(null);

const props = defineProps<{
	member: IUserDB | undefined;
}>();

const emit = defineEmits<{
	(e: 'close'): void;
	(e: 'update'): void;
}>();

const data = ref<IUser>({
	phone: props.member?.firstname ? props.member.firstname : '',
	firstname: props.member?.firstname ? props.member.firstname : '',
	lastname: props.member?.lastname ? props.member.lastname : '',
	email: props.member?.email ? props.member?.email : '',
	role: props.member?.role ? props.member?.role : Role.member,
	organizationType: OrganizationType.agency,
	organizationId: currentUser.value!.organizationId,
	status: props.member?.status ? props.member.status : UserStatus.waiting,
});
const { t } = useI18n();

async function handleAcceptClick() {
	const err = await validateUserData(data.value);

	if (err === null) {
		try {
			if (props.member === undefined) {
				await inviteUser(data.value);
				toast.success(t('page.members.toast.invited'));
			} else {
				await updateUser(data.value, props.member._id);
				toast.success(t('page.members.toast.updated'));
			}
			emit('update');
			emit('close');
		} catch (e: any) {
			errors.value = serverErrorsToLocalErrors(e);
		}
	} else {
		errors.value = err;
	}
}

async function handleCancelClick() {
	emit('close');
}
</script>

<template>
	<div class="memberForm">
		<form-plus :errors="errors" error-prefix="user">
			<field-input :labelSlug="t('user.fields.firstname.label')" name="firstname" v-model="data.firstname" />
			<field-input :labelSlug="t('user.fields.lastname.label')" name="lastname" v-model="data.lastname" />
			<field-input :labelSlug="t('user.fields.email.label')" name="email" v-model="data.email" />
			<field-select
				:labelSlug="t('user.fields.role.label')"
				name="role"
				v-model="data.role"
				:options="getSelectOptions(Role, 'role')"
			/>
		</form-plus>

		<div class="buttonContainer">
			<main-button class="grey" @click="handleCancelClick">{{ t('global.cancel') }}</main-button>
			<main-button @click="handleAcceptClick">
				{{ member === undefined ? t('global.invite') : t('global.modify') }}
			</main-button>
		</div>
	</div>
</template>

<style lang="scss">
.memberForm {
	.FormPlus {
		margin: 15px 0 30px;
	}

	.buttonContainer {
		display: flex;
		gap: 30px;
		justify-content: center;
	}
}
</style>
