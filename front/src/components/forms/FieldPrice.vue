<script setup lang="ts">
import {computed, ref, watch} from "vue";
import FieldLayout from "./FieldLayout.vue";
import {useFormatters} from "@/composition/formatters";
import type {price} from "@shared/types";

const props = defineProps<{
    name: string
    label?: string
	labelSlug?: string
    extra?: string
    mandatory?: boolean
    placeholder?: string
    disabled?: boolean
    modelValue: price|null
}>();
const emit = defineEmits<{
    (e: 'update:modelValue', value: price|null): void
	(e: 'focusin'): void
	(e: 'focusout'): void
}>();

const {formatPrice, decodePrice} = useFormatters();

const lastValue = ref<string>(formatPrice(props.modelValue, false, {thousandSeparator: ''}));
watch(() => props.modelValue, (val: price|null) => {
	if(val !== decodePrice(lastValue.value)) {
		lastValue.value = formatPrice(val, false, {thousandSeparator: ''});
	}
});

const value = computed({
    get(): string {
        return lastValue.value;
    },
    set(newValue: string) {
		lastValue.value = newValue;
		emit("update:modelValue", decodePrice(newValue));
    }
});

const hasFocus = ref<boolean>(false);
function focusin() {
	hasFocus.value = true;
}

function focusout() {
	hasFocus.value = false;
}

const hasValue = computed<boolean>(() => {
	return value.value !== undefined && value.value !== null && value.value.length > 0;
});

</script>

<template>
    <field-layout class="FieldPrice" :name="name" :mandatory="mandatory" :extra="extra" :label="label" :label-slug="labelSlug" :focus="hasFocus" :hasValue="hasValue">
        <input type="text" :id="name" :name="name" :placeholder="placeholder" v-model="value" :disabled="disabled" @focusin="focusin" @focusout="focusout"/>
    </field-layout>
</template>

<style lang="scss">
.FieldPrice {
	input {
		display: block;
		appearance: none;
		border: $cbrightBlue solid 1px;
		padding: 10px 16px;
		font-family: $fontMain;
		font-size: $fsform;
		font-weight: $fbold;
		color: $cformText;
		background-color: transparent;
		outline: none;
		width: 100%;
		border-radius: 8px;

		&::placeholder {
			color: $cformPlaceholder;
			font-weight: $fregular;
		}
	}
}
</style>