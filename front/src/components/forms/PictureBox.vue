<script setup lang="ts">

import {Picture} from "@shared/types";
import Iplus from "@/components/images/Iplus.vue";
import {useI18n} from "vue-i18n";
import {computed, ref} from "vue";
import {useApi} from "@/composition/api";
import Icross from "@/components/images/Icross.vue";

const props = defineProps<{
	picture: Picture|null
	placeholder?: string
	placeholderSlug?: string
	draggable?: boolean
}>();
const emit = defineEmits<{
	(e: 'delete'): void
	(e: 'order', source: Picture, target: Picture|null): void
}>();

const {t} = useI18n();
const {buildStaticUrl} = useApi();

const placeholder = computed<string|null>(() => {
	if(props.placeholder) {
		return props.placeholder;
	}
	else if(props.placeholderSlug) {
		return t(props.placeholderSlug);
	}
	else {
		return null;
	}
});

const picturePath = computed<string|null>(() => {
	if(props.picture && props.picture.path) {
		if(props.picture.path.startsWith("data:")) {
			return props.picture.path;
		}
		else {
			return buildStaticUrl(props.picture.path);
		}
	}
	else {
		return null;
	}
});

const thumbnailPath = computed<string|null>(() => {
    if(props.picture && props.picture.thumbnail) {
        return buildStaticUrl(props.picture.thumbnail);
    }
    else {
        return null;
    }
});

const isVideo = computed<boolean>(() => {
	return !!props.picture && !!props.picture.mimetype && props.picture.mimetype.startsWith("video");
});

// --- dragged
const dragging = ref<boolean>(false);
const draggingImage = ref<HTMLElement|null>(null);
const dragImgContainer = ref<HTMLElement>();

function dragStart(e: DragEvent) {
	if(!props.draggable) {
		return;
	}
	dragging.value = true;

	const img = document.createElement("div");
	img.style.width = "100px";
	img.style.height = "100px";
	img.style.backgroundPosition = "center center";
	img.style.backgroundSize = "cover";
	img.style.backgroundRepeat = "norepeat";
	img.style.backgroundImage = "url(" + picturePath.value + ")";
	document.getElementById("dragImage")!.appendChild(img);
	draggingImage.value = img;
	// e.target.appendChild(img);
	e.dataTransfer!.setDragImage(img, 0, 0);
	e.dataTransfer!.clearData();
	e.dataTransfer!.setData("picture", JSON.stringify(props.picture));
}
function dragEnd(_e: DragEvent) {
	if(!props.draggable) {
		return;
	}
	dragging.value = false;
	document.getElementById("dragImage")!.removeChild(draggingImage.value!);
	draggingImage.value = null;
}

// --- drop zone
const dragon = ref<number>(0);

function dragOver(e: DragEvent) {
	if(!props.draggable) {
		return;
	}
	const pic = e.dataTransfer!.types.includes("picture");
	if(pic && !dragging.value) {
		e.preventDefault();
	}
}
function dragEnter(e: DragEvent) {
	if(!props.draggable) {
		return;
	}
	const pic = e.dataTransfer!.types.includes("picture");
	if(pic && !dragging.value) {
		e.preventDefault();
		dragon.value++;
	}
}
function dragLeave(e: DragEvent) {
	if(!props.draggable) {
		return;
	}
	const pic = e.dataTransfer!.types.includes("picture");
	if(pic && !dragging.value) {
		e.preventDefault();
		dragon.value--;
	}
}

function drop(e: DragEvent) {
	let picString = e.dataTransfer!.getData("picture");
	if(picString) {
		const pic = JSON.parse(picString);
		if(!props.picture || pic.path !== props.picture.path) {
			dragon.value = 0;
			emit("order", pic, props.picture);
		}
	}
}

</script>


<template>
	<div :class="'PictureBox' + (picturePath ? ' filled':' empty') + (dragon > 0 ? ' dragover': '')" @dragover="dragOver" @dragenter="dragEnter" @dragleave="dragLeave" @drop="drop">
    <div class="picture" v-if="picturePath" draggable="true" @dragstart="dragStart" @dragend="dragEnd">
			<video v-if="isVideo" :src="picturePath" controls preload="metadata" :poster="thumbnailPath!" crossorigin="anonymous"/>
			<img v-else :src="picturePath" alt="picture" crossorigin="anonymous"/>
		</div>
		<div v-else class="placeholder">
			<div class="plus"><iplus/></div>
			<div v-if="placeholder" class="text">{{placeholder}}</div>
		</div>
		<div class="delete">
			<div class="delete-button" @click="emit('delete')">
				<icross/>
			</div>
		</div>
		<div class="dropzone"/>
		<div class="dragimg" ref="dragImgContainer"/>
	</div>
</template>

<style lang="scss">
.PictureBox {
	border: $cbrightBlue dashed 2px;
	border-radius: $radius;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;

	.picture {
		width: 100%;
		height: 100%;
    padding: 15px 50px;

		img, video {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		&.dragging {
			border-radius: $radius;
			opacity: 0.5;
		}
	}

	.placeholder {
		padding: 60px;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 18px;

    .text {
			font-size: $fsmedium;
			font-weight: $fbold;
			color: $csearchGray;
			line-height: 1.3em;
			max-width: 180px;
			text-align: center;
      min-width: max-content;
		}
	}

	.delete {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		display: none;
		align-items: center;
		justify-content: center;
		color: white;
		pointer-events: none;

		.delete-button {
			width: 30px;
			height: 30px;
			cursor: pointer;
			pointer-events: auto;

			.icon {
				width: 100%;
				height: 100%;
			}
		}

	}

	.dropzone {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		display: none;
		background-color: rgba(255, 255, 255, 0.8);
	}

	&.filled:hover {
		.delete {
			display: flex;
		}
	}

	&.dragover {
		.dropzone {
			display: block;
		}
	}
}
</style>