<script setup lang="ts">
import {computed, ref, watchEffect} from "vue";
import FieldLayout from "@/components/forms/FieldLayout.vue";

const props = defineProps<{
    name: string
    label?: string
    extra?: string
    mandatory?: boolean
    placeholder?: string
    disabled?: boolean
    modelValue: any
    options: Array<string|{label: string, value: any}>|(() => Array<string|{label: string, value: any}>)|(() => Promise<Array<string|{label: string, value: any}>>)
}>();

// events
const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void
}>();

// data
const value = computed({
    // getter
    get(): any {
        return props.modelValue;
    },
    // setter
    set(newValue: any) {
        emit("update:modelValue", newValue);
    }
});

// --- define options
let options = ref<Array<{label: string, value: any}>>([]);

watchEffect(async () => {
    let result: Array<string|{label: string, value: any}>;

    if(Array.isArray(props.options)) {
        result = props.options;
    }
    else {  // function
        let ret = props.options();
        if(Array.isArray(ret)) {
            result = ret;
        }
        else {  // promise
            result = await ret;
        }
    }

    options.value = result.map(entry => {
        if(typeof entry === "object") {
            return entry;
        }
        else {
            return {label: entry, value: entry}
        }
    });
});
</script>

<template>
    <field-layout class="FieldSelect" :name="props.name" :mandatory="props.mandatory" :label="props.label" :extra="props.extra">
        <select :id="props.name" :name="props.name" v-model="value" :disabled="props.disabled">
          <option value="" disabled selected hidden v-if="placeholder">{{placeholder}}</option>
          <option v-for="opt in options" :value="opt.value">{{opt.label}}</option>
        </select>
    </field-layout>
</template>

<style lang="scss">
.FieldSelect {

    select {
		display: block;
		appearance: none;
		border: $cbrightBlue solid 1px;
		padding: 10px 16px;
		font-family: $fontMain;
		font-size: $fsform;
		font-weight: $fbold;
		color: $cformText;
		outline: none;
		width: 100%;
		background: transparent url(./select-down.svg)  no-repeat calc(100% - 18px) center;
		background-size: 16px auto;
    border-radius: 8px;

		&::placeholder {
			color: $cformPlaceholder;
			font-weight: $fregular;
		}
	}
}
</style>