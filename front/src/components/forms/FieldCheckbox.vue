<script setup lang="ts">
// props
import { CheckIcon } from 'lucide-vue-next';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import FieldLayout from '@/components/forms/FieldLayout.vue';

const props = defineProps<{
	name: string;
	label?: string;
	labelSlug?: string;
	extra?: string;
	mandatory?: boolean;
	placeholder?: string;
	disabled?: boolean;
	modelValue: boolean;
}>();
const emit = defineEmits<{
	(e: 'update:modelValue', value: boolean): void;
}>();

const { t } = useI18n();

// data
const value = computed({
	// getter
	get(): boolean {
		return props.modelValue;
	},
	// setter
	set(newValue: boolean) {
		emit('update:modelValue', newValue);
	},
});

const theLabel = computed<string | null>(() => {
	if (props.label) {
		return props.label;
	} else if (props.labelSlug) {
		return t(props.labelSlug);
	} else {
		return null;
	}
});

function toggle() {
	value.value = !value.value;
}
</script>

<template>
	<field-layout class="FieldCheckbox" :name="props.name" :mandatory="props.mandatory" :extra="props.extra" hasValue>
		<div class="checkbox" @click="toggle">
			<div :class="'check-box ' + (value ? 'on' : 'off')"><CheckIcon :size="18" color="white" /></div>
			<label v-if="theLabel">
				{{ theLabel }}
			</label>
		</div>
	</field-layout>
</template>

<style lang="scss">
.FieldCheckbox {
	.checkbox {
		display: flex;
		cursor: pointer;
		align-items: center;

		.check-box {
			border: $grey-dark solid 1px;
			min-height: 20px;
			min-width: 20px;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: white;
			margin-right: 17px;

			.icon {
				color: white;
				width: 10px;
			}

			&.on {
				background-color: $grey-dark;
			}
		}

		label {
			color: $grey-dark;
			font-size: 14px;
			cursor: pointer;
		}

		&:hover {
			.check-box.off {
				.icon {
					color: $cwhite;
				}
			}

			label {
				color: $cbrightBlue;
			}
		}
	}

	&.in {
		.InnerField {
			border: none;
		}
	}
}
</style>
