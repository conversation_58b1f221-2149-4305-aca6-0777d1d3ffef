<script setup lang="ts">
import { Picture } from '@shared/types';
import { computed, ref } from 'vue';

import { IMAGE_MIMETYPES, VIDEO_MIMETYPES } from '@/common';
import PictureBox from '@/components/forms/PictureBox.vue';

const props = defineProps<{
	label?: String;
	modelValue: Picture | null;
	acceptVideo?: boolean;
	placeholder?: string;
	placeholderSlug?: string;
	draggable?: boolean;
}>();
const emit = defineEmits<{
	(e: 'update:modelValue', value: Picture | null): void;
}>();

const dragging = ref<number>(0);

function dragEnter(e: Event) {
	e.preventDefault();
	dragging.value++;
}
function dragOver(e: Event) {
	e.preventDefault();
}
function dragLeave(e: Event) {
	e.preventDefault();
	dragging.value--;
}

const acceptedMimeTypes = computed<string[]>(() => {
	if (props.acceptVideo) {
		return [...IMAGE_MIMETYPES, ...VIDEO_MIMETYPES];
	} else {
		return IMAGE_MIMETYPES;
	}
});
function filesDropped(e: any) {
	e.preventDefault();
	dragging.value = 0;

	console.log('files dropped: ', e.dataTransfer);

	let pic = e.dataTransfer.getData('picture');
	if (pic) {
		const newPic = JSON.parse(pic);
		emit('update:modelValue', newPic);
	} else {
		const files = [...e.dataTransfer.files];
		const acceptedFiles = files.filter((f) => acceptedMimeTypes.value.includes(f.type));

		if (acceptedFiles.length > 0) {
			uploadFile(acceptedFiles[0]);
		}
	}
}

function uploadFile(file: File) {
	const reader: FileReader = new FileReader();

	reader.onload = (e: any) => {
		const newPic: Picture = {
			path: e.currentTarget.result,
			mimetype: file.type,
		};
		emit('update:modelValue', newPic);
	};
	reader.readAsDataURL(file);
}

const picture = computed<Picture | null>(() => {
	if (props.modelValue) {
		return props.modelValue;
	} else {
		return null;
	}
});

function deletePic() {
	emit('update:modelValue', null);
}

const fileInput = ref<HTMLInputElement>();

async function fileUploaded(e: Event) {
	const files: FileList = (e.target as any).files;

	if (files && files.length > 0) {
		await uploadFile(files[0]);
	}
}

function startUpload() {
	if (fileInput.value) {
		fileInput.value.click();
	}
}
</script>

<template>
	<div
		:class="'FieldPicture' + (dragging > 0 ? ' dragging' : '')"
		@drop="filesDropped"
		@dragenter="dragEnter"
		@dragover="dragOver"
		@dragleave="dragLeave"
	>
		<label v-if="label">{{ label }}</label>
		<picture-box
			:placeholder="placeholder"
			:placeholder-slug="placeholderSlug"
			:picture="picture"
			@delete="deletePic"
			@click="startUpload"
			:draggable="draggable"
		/>
		<input
			type="file"
			ref="fileInput"
			style="display: none"
			@change="fileUploaded"
			:accept="acceptedMimeTypes.join(',')"
		/>
	</div>
</template>

<style lang="scss">
.PictureBox {
	cursor: pointer;
}

.FieldPicture {
	label {
		display: block;
		font-size: 13px;
		font-weight: $fmedium;
		line-height: normal;
		color: $csearchGray;
		margin-bottom: 6px;
	}

	&.dragging {
		.PictureBox {
			border: $cformPictureDragging solid 4px;
		}
	}
}
</style>
