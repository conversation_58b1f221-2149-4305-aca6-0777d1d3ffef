<script setup lang="ts">
// props
import {computed} from "vue";
import moment from "moment-timezone";
import Datepicker from 'vue3-datepicker';
import FieldLayout from "@/components/forms/FieldLayout.vue";
import type {timestamp} from "@shared/types";
import Icross from "@/components/images/Icross.vue";

const props = defineProps<{
    name: string
    label?: string
    extra?: string
    mandatory?: boolean
    placeholder?: string
    disabled?: boolean
    modelValue: timestamp|null
    min?: timestamp
    max?: timestamp
	nullable?: boolean
	format?: string
}>();

// events
const emit = defineEmits<{
    (e: 'update:modelValue', value: timestamp|null): void
}>();

// data
const value = computed<Date|undefined>({
    // getter
    get(): Date|undefined {
        if(props.modelValue) {
            return moment(props.modelValue).toDate();
        }
        else {
            return undefined;
        }
    },
    // setter
    set(newValue: Date|undefined) {
        if(newValue) {
            emit("update:modelValue", newValue.valueOf());
        }
        else {
            emit("update:modelValue", null);
        }
    }
});

// ---limits
const min = computed<Date|undefined>(() => {
    if(props.min) {
        return moment(props.min).toDate();
    }
    else {
        return undefined;
    }
});
const max = computed<Date|undefined>(() => {
    if(props.max) {
        return moment(props.max).toDate();
    }
    else {
        return undefined;
    }
});

const inputFormat = computed<string>(() => {
	return props.format ? props.format : "dd/MM/yyyy";
});
</script>

<template>
    <field-layout :class="'FieldDate' + (nullable ? ' with-null':'')" :name="props.name" :mandatory="props.mandatory" :label="props.label" :extra="props.extra">
        <!--suppress TypeScriptValidateTypes -->
		<datepicker :id="props.name"
					:name="props.name"
					v-model="value"
					:disabled="!!disabled"
					:inputFormat="inputFormat"
					:placeholder="placeholder? placeholder:''"
                    :upper-limit="max"
                    :lower-limit="min"
        />
		<div v-if="nullable" class="button-null" @click="value = undefined"><Icross/></div>
    </field-layout>
</template>

<style lang="scss">

.FieldDate {
    .v3dp__datepicker {
		--elem-hover-bg-color: $cdimColor;
		--elem-hover-color: $cwhite;
		--elem-selected-bg-color: $cmainColor;
    }

    .v3dp__popout {
        width: calc(100% - 46px) !important;
        left: 23px;
        border: $cborder solid 1px;
        margin-top: -2px;
    }

    input {
		display: block;
		appearance: none;
		border: $cbrightBlue solid 1px;
		padding: 10px 16px;
		font-family: $fontMain;
		font-size: $fsform;
		font-weight: $fbold;
		color: $cformText;
		background-color: transparent;
		outline: none;
		width: 100%;border-radius: 8px;

		&::placeholder {
			color: $cformPlaceholder;
			font-weight: $fregular;
		}
    }

	&.with-null {
		.input {
			display: flex;
			width: 100%;
		}
		.v3dp__datepicker {
			flex: 1 1 0;
			border-right: none;

			input {
				border-right: none;
			}
		}

		.button-null {
			flex: 0 0 30px;
			background-color: white;
			border: $cborder solid 2px;
			border-left: none;
			color: $cborder;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;

			&:hover {
				color: black;
			}
		}
	}
}
</style>