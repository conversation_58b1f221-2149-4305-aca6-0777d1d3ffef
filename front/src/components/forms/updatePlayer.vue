<script setup lang="ts">

import FieldInput from "@/components/forms/FieldInput.vue";
import FieldInteger from "@/components/forms/FieldInteger.vue";
import MainButton from "@/components/ui/MainButton.vue";
import FormPlus from "@/components/forms/FormPlus.vue";
import {useI18n} from "vue-i18n";
import {IPlayerDB} from "@shared/crudTypes";
import {ref} from "vue";
import UnderlineTitle from "@/components/ui/UnderlineTitle.vue";
import GreyBox from "@/components/ui/GreyBox.vue";
import {useApi} from "@/composition/api";
import {useValidators} from "@/composition/validators";
import FieldSelect from "@/components/forms/FieldSelect.vue";
import {AvailabilitiesOption} from "@shared/enums";
const {t} = useI18n()
const {updatePlayer} = useApi()
const {validatePlayerData}= useValidators()

const props = defineProps<{
  player: IPlayerDB
}>();
 const emit = defineEmits<{
   (e: 'close'): void
 }>();

const errors = ref<any>(null);
const booleanOption = [{label: t('global.yes'), value: true},{label: t('global.no'), value: false}]
const availabilitiesOption = [{label: t('player.focus.availabilityOption.available'), value: AvailabilitiesOption.available},{label: t('player.focus.availabilityOption.signed'), value: AvailabilitiesOption.signed},{label: t('player.focus.availabilityOption.ongoingNegotiations'), value: AvailabilitiesOption.ongoingNegotiations}]

const data = ref<any>({
  availabilities: props.player.availabilities ? props.player.availabilities : true,
  email: props.player.email ? props.player.email : "",
  phoneNumber: props.player.phoneNumber ? props.player.phoneNumber : "",
  salary: props.player.salary ? props.player.salary : "",
  accommodation: props.player.accommodation ? props.player.accommodation : true,
  carFunction: props.player.carFunction ? props.player.carFunction : true,
});

async function handleModifyClick() {
  const err = await validatePlayerData(data.value);

  if (err === null) {
    try{
      await updatePlayer(data.value, props.player!._id)
      emit("close")
    }catch (e){
      console.log("internal error")
    }
  } else {
    errors.value = err;
  }
}
</script>

<template>
  <div class="playerForm">
    <underline-title>{{t('player.field.title')}}</underline-title>
    <grey-box>{{t('player.field.text')}}</grey-box>
    <form-plus :errors="errors" error-prefix="agency" >
      <field-select :labelSlug="t('player.focus.recruitments.availabilities')" name="availabilities" :options="availabilitiesOption" v-model="data.availabilities"/>
      <field-input :labelSlug="t('player.focus.recruitments.email')" name="email" v-model="data.email"/>
      <field-integer :labelSlug="t('player.focus.recruitments.phoneNumber')" name="phoneNumber" v-model="data.phoneNumber"/>
      <field-integer :labelSlug="t('player.focus.recruitments.salary')" name="salary" v-model="data.salary"/>
      <field-select :labelSlug="t('player.focus.recruitments.accommodation')" name="accommodation" :options="booleanOption" v-model="data.accommodation"/>

    </form-plus>
    <div class="buttonContainer">
      <main-button class="grey" @click="emit('close')">
        {{t('player.field.cancel')}}
      </main-button>
      <main-button @click="handleModifyClick()">
        {{t('player.field.save')}}
      </main-button>
    </div>
  </div>
</template>

<style lang="scss">
.playerForm {
  .buttonContainer {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 30px;

    .MainButton {
      width: fit-content;
    }
  }
}

</style>