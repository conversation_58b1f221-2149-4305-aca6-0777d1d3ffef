<script setup lang="ts">
import {type Component, computed, ref, useSlots, watchEffect} from "vue";
import FieldLayout from "./FieldLayout.vue";
import {useI18n} from "vue-i18n";
import debounce from "lodash.debounce";
import Loader from "@/components/ui/Loader.vue";

const props = defineProps<{
    name: string
    label?: string
	labelSlug?: string
    extra?: string
    mandatory?: boolean
    placeholder?: string
	placeholderSlug?: string
    disabled?: boolean
    modelValue: any

	searchFunction?: (term: string) => Promise<any[]>
	searchEmptyMessage?: string
	suggestionComponent?: Component|string
	labelKey?: string
	resultKey?: string		// key of the property returned in modelValue. If null, the entire object is returned.
	formatter?: (item: any) => string
	mode?: "strict"|"free"	// strict, on ne peut que sélectionner une valeur dans la liste. free, on peut renvoyer n'importe quel string
}>();
const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void
}>();

const {t} = useI18n();
const slots = useSlots();

const localValue = ref<string>("");
const searching = ref<boolean>(false);
const suggestions = ref<any[]|null>(null);
const focus = ref<boolean>(false);
const searchError = ref<string|null>(null);

watchEffect(() => {
	localValue.value = formatItem(props.modelValue);
	suggestions.value = null;
	searchError.value = null;
	searching.value = false;
});

const selectionMode = computed<"strict"|"free">(() => {
	return props.mode === "free" ? "free" : "strict";
});

const value = computed({
    get(): any {
        return localValue.value;
    },
    set(newValue: any) {
		localValue.value = newValue;
		if(localValue.value.trim().length > 0) {
			debouncedSearch();
		}
		else {
			suggestions.value = null;
			searchError.value = null;
			searching.value = false;
		}
    }
});

const thePlaceholder = computed<string|undefined>(() => {
	if(props.placeholder) {
		return props.placeholder;
	}
	else if(props.placeholderSlug) {
		return t(props.placeholderSlug);
	}
	else {
		return undefined;
	}
});

async function search() {
	if(value.value.trim().length === 0) {
		searching.value = true;
		searchError.value = null;
		suggestions.value = null;
	}
	else {
		try {
			searching.value = true;
			searchError.value = null;
			if (props.searchFunction) {
				suggestions.value = await props.searchFunction(value.value);
			}
			else {
				suggestions.value = [];
			}
		}
		catch (err: any) {
			console.log("Error searching: ", err);
			searchError.value = err.toString();
		}
		searching.value = false;
	}
}
const debouncedSearch = debounce(search, 300);

const listOpened = computed<boolean>(() => {
	return focus.value && (searching.value || !!suggestions.value || !!searchError.value);
});

const searchEmptyMessage = computed<string>(() => {
	return props.searchEmptyMessage || "no suggestion found.";
});

function gainFocus() {
	focus.value = true;
}
function lostFocus() {
	if(selectionMode.value === "strict") {
		localValue.value = formatItem(props.modelValue);
	}
	else {
		emit("update:modelValue", getResult(localValue.value));
	}
	focus.value = false;
}

function getResult(val: any) {
	if(props.resultKey) {
		return val[props.resultKey];
	}
	else {
		return val;
	}
}

function formatItem(item: any) : string {
	// console.log("formatItem: ", item, " / labelKey: ", props.labelKey);
	if(props.formatter) {
		return props.formatter(item);
	}
	else if(item) {
		if (props.labelKey) {
			return item[props.labelKey];
		}
		else {
			if (item.name) {
				return item.name;
			}
			else {
				return item.toString();
			}
		}
	}
	else {
		return "";
	}
}

const input = ref<HTMLElement>();
const list = ref<HTMLElement>();

function selectSuggestion(item: any) {
	emit("update:modelValue", getResult(item));
	if(input.value) {
		input.value.blur();
	}
}

function computeY(item: HTMLElement): number {
	return item.offsetTop + (item.offsetParent ? computeY(item.offsetParent as HTMLElement) : -window.scrollY);
}

const listClass = computed<any>(() => {
	if(listOpened.value && input.value && list.value && localValue.value) {
		const inputY = computeY(input.value);
		const inputHeight = input.value.offsetHeight;
		const windowHeight = window.innerHeight;
		const listHeight = list.value.offsetHeight;
		if(inputY + inputHeight + listHeight > windowHeight) {
			return "top";
		}
	}

	return "bottom";
});

</script>

<template>
    <field-layout :class="'FieldAutocomplete ' + (listOpened ? 'open':'close') + ' ' + listClass" :name="name" :mandatory="mandatory" :extra="extra" :label="label" :label-slug="labelSlug" :disabled="disabled">
		<div :class="'input-container' + (slots.default || slots.after ? ' with-slots':'')">
			<slot/>
			<input type="text" :id="name" :name="name" :placeholder="thePlaceholder" v-model="value" :disabled="disabled" @blur="lostFocus" @focus="gainFocus" ref="input"/>
			<slot name="after"/>
		</div>
		<div v-if="listOpened" class="list" ref="list">
			<loader v-if="searching"/>
			<div v-else-if="searchError" class="error">{{searchError}}</div>
			<div v-else-if="suggestions && suggestions.length === 0" class="empty">{{searchEmptyMessage}}</div>
			<template v-else>
				<template v-for="(item, idx) in suggestions">
					<component v-if="suggestionComponent" :key="'suggestion-comp-' + idx" :is="suggestionComponent" :item="item" class="suggestion" @mousedown="selectSuggestion(item)"/>
					<div v-else :key="'suggestion-div-' + idx" class="suggestion" v-html="formatItem(item)" @mousedown="selectSuggestion(item)"/>
				</template>

			</template>
		</div>
    </field-layout>
</template>

<style lang="scss">
.FieldAutocomplete {
	position: relative;

	.input-container {
		border-radius: $radius;
		border: $cbrightBlue solid 1px;
		background: $cwhite;
		padding: 0 12px;
		display: flex;
		gap: 8px;
		align-items: center;

		input {
			display: block;
			appearance: none;
			border: none;
			padding: 10px 0px;
			font-family: $fontMain;
			font-size: $fsform;
			font-weight: $fbold;
			color: $cformText;
			background-color: transparent;
			outline: none;
			width: 100%;
			border-radius: 8px;

			&::placeholder {
				color: $cformPlaceholder;
				font-weight: $fregular;
			}
		}
	}

	.list {
		position: absolute;
		z-index: 101;
		border: $cborder solid 1px;
		left: 0;
		min-width: 100%;
		background-color: white;
		text-align: left;
		max-height: 50vh;
		overflow: auto;

		.Loader {
			.v-pulse {
				background-color: $cbrightBlue;
			}
		}

		.empty {
			color: $cbrightBlue;
		}

		.error {
			padding: 0;
			margin: 0;
		}

		.suggestion {
			padding: 8px $gap2;
			cursor: pointer;
			color: $cbrightBlue;

			&:nth-child(odd) {
				background-color: $cbackgroundGray;
			}

			&:hover {
				background-color: $cbrightBlue;
				color: white;
			}
		}
	}

	&.open {

		&.top {
			input {
				border-top-left-radius: 0;
				border-top-right-radius: 0;
			}

			.list {
				bottom: calc(100% - 1px);
				border-top-left-radius: $radius;
				border-top-right-radius: $radius;
				box-shadow: 0px -4px 10px 0px rgba(0, 37, 147, 0.10);
			}
		}

		&.bottom {
			input {
				border-bottom-left-radius: 0;
				border-bottom-right-radius: 0;
			}

			.list {
				top: calc(100% - 1px);
				border-bottom-left-radius: $radius;
				border-bottom-right-radius: $radius;
				box-shadow: 0px 4px 10px 0px rgba(0, 37, 147, 0.10);
			}
		}
	}
}
</style>