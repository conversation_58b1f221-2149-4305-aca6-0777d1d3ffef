<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import Logo from "@/components/images/Logo.vue";
import {Routes} from "@/enums";
import MainButton from "@/components/ui/MainButton.vue";
import Router from "@/router";

// const props = defineProps<{
//     
// }>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

</script>

<template>
	<div class="PublicMenu">
		<router-link :to="{name: Routes.home}" custom v-slot="{ navigate }">
			<logo @click="navigate" role="link"/>
		</router-link>
		<div class="menu">
			<router-link :to="{name: Routes.contact}">{{t('routes.contact')}}</router-link>
			<template v-if="!isAuth">
				<router-link :to="{name: Routes.login}">{{t('routes.login')}}</router-link>
				<router-link :to="{name: Routes.register}" custom v-slot="{navigate}">
					<MainButton label-slug="routes.register" @click="navigate"/>
				</router-link>
			</template>
			<template v-else>
				<router-link :to="{name: Routes.app}" custom v-slot="{navigate}">
					<MainButton label-slug="routes.app" @click="navigate"/>
				</router-link>
			</template>
		</div>
	</div>
</template>

<style lang="scss">
.PublicMenu {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: $gap;
	background-color: $cmainBackground;
	box-shadow: $shadow;
	padding: 0 $gap;

	.Logo {
		height: 80px;
		cursor: pointer;
	}

	.menu {
		display: flex;
		align-items: center;
		gap: $gap;

		> a {
			color: $cmainColor;

			&:hover {
				color: $cdimColor;
			}

			&.active {
				text-decoration: underline;
			}
		}
	}
}
</style>