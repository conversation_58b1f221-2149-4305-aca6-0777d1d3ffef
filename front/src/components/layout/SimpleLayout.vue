<script setup lang="ts">

import Logo from "@/components/images/Logo.vue";
import Box from "@/components/ui/Box.vue";
import UnderlineTitle from "@/components/ui/UnderlineTitle.vue";
import GreyBox from "@/components/ui/GreyBox.vue";
import {useSlots} from "vue";
import SwitchLang from "@/components/ui/SwitchLang.vue";
import {Routes} from "@/enums";

const slots = useSlots();
</script>

<template>
	<div class="simpleLayout">
		<div class="simpleLayoutHeader">
			<router-link :to="{name: Routes.appHome}"><Logo/></router-link>
			<SwitchLang/>
		</div>
		<div class="background"/>
		<div class="simpleLayoutBoxContainer">
			<Box>
				<underline-title v-if="slots.title">
					<slot name="title"/>
				</underline-title>
				<grey-box v-if="slots.subtitle">
					<slot name="subtitle"/>
				</grey-box>
				<slot/>
			</Box>
			<slot name="after"/>
		</div>
	</div>
</template>

<style lang="scss">
.simpleLayout {
	display: flex;
	flex-direction: column;
	height: 100vh;

	.simpleLayoutHeader {
		min-height: 80px;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		z-index: 1;
		background: $cwhite;
		box-shadow: $shadow;

		.switchLang {
			position: absolute;
			right: 30px;
		}
	}

	.background {
		background: white url("../../assets/background.png") no-repeat center center;
		background-size: cover;
		height: 100vh;
		position: fixed;
		width: 100vw;
		z-index: 0;
	}

	.FormPlus {
		margin: 17px 0 30px 0;
	}

	.simpleLayoutBoxContainer {
		position: relative;
		flex: 1 1 0;
		padding: 70px;
		overflow: auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: $gap;

		.Box {
			margin: 0 auto;
			// max-width: 730px;
			//transform: translateY(-50%);
			//top: 50%;
			border-radius: 20px;
			box-shadow: $shadow;
		}
	}
}
</style>