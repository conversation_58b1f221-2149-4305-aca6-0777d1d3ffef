<script setup lang="ts">
import { MenuIcon } from 'lucide-vue-next';
import { AvatarFallback, AvatarImage, AvatarRoot } from 'radix-vue';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { useApi } from '@/composition/api';
import { useFormatters } from '@/composition/formatters';
import { useAuth } from '@/composition/snark-auth';
import { Routes } from '@/enums';

import { Button, Container, Typography } from '../ui';
import navigation from './navigation/navigation';

const { t } = useI18n();
const { currentUser } = useAuth();
const { buildStaticUrl } = useApi();
const { playerFullname } = useFormatters();

const isCollapsed = ref(false);

const imageUrl = computed<string>(() => {
	if (currentUser.value?.picture?.path) {
		if (currentUser.value?.picture?.path.startsWith('data:')) {
			return currentUser.value?.picture?.path;
		} else {
			return buildStaticUrl(currentUser.value?.picture?.path);
		}
	}
	return '';
});

const toggleSidebar = () => {
	isCollapsed.value = !isCollapsed.value;
};
</script>

<template>
	<aside class="sideBar" :class="{ collapsed: isCollapsed }">
		<Container class="sideBar__upper">
			<Button variant="outline" size="icon" class="sideBar__upper__sidebarButton" @click="toggleSidebar">
				<MenuIcon :size="18" />
			</Button>

			<Container class="sideBar__upper__user">
				<AvatarRoot :class="`avatar ${isCollapsed ? 'avatar--collapsed' : ''}`">
					<AvatarImage :src="imageUrl" alt="Profile image" />
					<AvatarFallback :delay-ms="600">
						<Typography :variant="isCollapsed ? 'subtitle' : 'h3'" class="avatar__fallback">
							{{ `${currentUser?.firstname[0]}${currentUser?.lastname[0]}` }}
						</Typography>
					</AvatarFallback>
				</AvatarRoot>
				<Container v-if="!isCollapsed">
					<Typography variant="medium">{{ playerFullname(currentUser) }}</Typography>
					<router-link :to="{ name: Routes.profile }" custom v-slot="{ navigate }">
						<Button variant="outline" size="sm" @click="navigate">
							{{ t('routes.editProfile') }}
						</Button>
					</router-link>
				</Container>
			</Container>
		</Container>

		<Container class="sideBar__lower">
			<router-link
				v-for="navItem in navigation(currentUser!)"
				:to="{ name: navItem.url }"
				:key="navItem.url"
				:class="navItem.class || ''"
			>
				<component :is="navItem.icon" class="icon" />
				<Typography v-if="!isCollapsed" size="lg">
					{{ t(navItem.label) }}
				</Typography>
			</router-link>
		</Container>
	</aside>
</template>

<style lang="scss">
.sideBar {
	flex-direction: column;
	display: flex;
	gap: 30px;
	min-width: 268px;
	border-right: 1px solid $cborderGray;
	background-color: $white;

	&.collapsed {
		min-width: 80px;

		.sideBar__lower a {
			justify-content: center;
			padding: 12px;
			padding-left: 24px;
			.icon {
				margin: 0;
			}
		}
	}

	&__upper {
		gap: 28px;
		padding: 24px;
		padding-bottom: 28px;
		border-bottom: 1px solid $grey-light;

		.avatar {
			width: 64px;
			height: 64px;

			&--collapsed {
				width: 40px;
				height: 40px;
			}

			&__fallback {
				letter-spacing: 2px;
				text-indent: 2px;
			}
		}

		&__sidebarButton {
			border-radius: 50%;
		}

		&__user {
			flex-direction: row;
			align-items: center;
			gap: 20px;

			> * {
				gap: 12px;
			}
		}
	}

	&__lower {
		gap: 12px;

		a {
			display: flex;
			gap: 20px;
			align-items: center;
			color: $charcoal;
			font-size: 16px;
			padding: 12px 24px;
			margin-right: 24px;
		}

		.icon {
			min-height: 20px;
			min-width: 20px;
		}

		.router-link-exact-active,
		.router-link-active:not(.homelink) {
			background-color: #d1e9ff80;
			border-radius: 0 10px 10px 0;

			> * {
				color: $blue-primary;
			}

			.icon path {
				fill: $cbrightBlue;
			}

			&::before {
				display: block;
			}
		}
	}
}
</style>
