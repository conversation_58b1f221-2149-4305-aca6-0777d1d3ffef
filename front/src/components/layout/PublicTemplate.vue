<script setup lang="ts">

import PublicMenu from "@/components/layout/PublicMenu.vue";
import PublicFooter from "@/components/layout/PublicFooter.vue";

</script>

<template>
    <div class="PublicTemplate">
        <public-menu/>
        <div class="main-content">
            <slot/>
        </div>
		<public-footer/>
    </div>
</template>

<style lang="scss">
.PublicTemplate {
	position: relative;
    padding-top: $publicMenuHeight;

    .PublicMenu {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
		height: $publicMenuHeight;
		z-index: 100;
    }

	.main-content {
		margin: $gap;
		min-height: calc(100vh - $publicMenuHeight - $publicFooterHeight - 3 * $gap);
    width: 100%;
	}

	.PublicFooter {
		margin-top: calc($gap * 2);
	}
}
</style>