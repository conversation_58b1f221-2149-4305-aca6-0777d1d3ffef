<script setup lang="ts">
import AppMenu from '@/components/layout/AppMenu.vue';
import SideBar from '@/components/layout/SideBar.vue';

const props = defineProps<{
	haveSideBar?: boolean;
}>();
</script>

<template>
	<div class="AppTemplate">
		<SideBar v-if="haveSideBar" />
		<div class="templateContainer">
			<app-menu />
			<div class="main-content">
				<slot />
			</div>
		</div>
	</div>
</template>

<style lang="scss">
.AppTemplate {
	display: flex;
	min-height: 100vh;
	width: 100%;

	.templateContainer {
		display: flex;
		flex-direction: column;
		flex: 1 1 0;

		.main-content {
			padding: 30px;
			flex: 1 1 0;
			overflow-y: auto;
		}
	}
}
</style>
