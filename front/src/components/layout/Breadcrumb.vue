<script setup lang="ts">
interface BreadcrumbItem {
  label: string;
  href?: string;
}

defineProps<{
  items: BreadcrumbItem[];
}>();
</script>

<template>
  <nav class="breadcrumb" aria-label="Breadcrumb">
    <ol class="breadcrumb__list">
      <li v-for="(item, index) in items" :key="index" class="breadcrumb__item">
        <RouterLink v-if="item.href && index !== items.length - 1" :to="{ name: item.href }" class="breadcrumb__link">
          {{ item.label }}
        </RouterLink>
        <span v-else class="breadcrumb__current">
          {{ item.label }}
        </span>
        <span v-if="index !== items.length - 1" class="breadcrumb__separator">/</span>
      </li>
    </ol>
  </nav>
</template>

<style>
.breadcrumb {
  font-size: 16px;
  font-family: sans-serif;
  color: #4b5563;
}

.breadcrumb__list {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb__item {
  display: flex;
  align-items: center;
}

.breadcrumb__link {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb__link:hover {
  color: #6b7280;
}

.breadcrumb__separator {
  margin: 0 8px;
  color: #9ca3af;
}

.breadcrumb__current {
  color: #111827;
  font-weight: 500;
}
</style>
