<script setup lang="ts">
import IProfile from '@/components/images/Iprofile.vue';
import MainButton from '@/components/ui/MainButton.vue';
import SwitchLang from '@/components/ui/SwitchLang.vue';
import { Routes } from '@/enums';
import router from '@/router';

import Ileft from '../images/pagination/Ileft.vue';
import { Button, Container } from '../ui';

const goBack = () => router.back();
</script>

<template>
	<Container class="AppMenu">
		<Button variant="ghost" size="icon-rounded" class="backButton" @click="goBack">
			<Ileft class="backButton--icon" />
		</Button>
		<Container class="AppMenu__right">
			<switch-lang />
		</Container>
	</Container>
</template>

<style lang="scss">
.AppMenu {
	flex-direction: row;
	gap: 30px;
	height: 80px;
	align-items: center;
	justify-content: space-between;
	padding: 17px 32px;
	background-color: transparent;

	&__right {
		flex-direction: row;
		align-items: center;
		gap: 20px;

		.MainButton {
			border-radius: 50%;
			height: 45px;
			width: 45px;
			padding: 0;
		}

		.searchBar {
			position: relative;
			flex: initial;

			.icon {
				position: absolute;
				top: 50%;
				left: 20px;
				transform: translate(0, -50%);
			}

			input {
				width: 370px;
				height: 40px;
				border: 0;
				background: $cbackgroundGray;
				border-radius: 40px;
				padding: 0 20px 0 46px;
				color: $csearchGray;
				font-size: 12px;
				font-weight: $fregular;

				&:focus-visible {
					outline: none;
				}
			}
		}
	}
}

.backButton {
	background-color: $white;
	justify-content: center;

	&--icon {
		width: 20px;
		height: 20px;
	}
}
</style>
