<script setup lang="ts">

import FieldInput from "@/components/forms/FieldInput.vue";
import {computed, ref} from "vue";
import {IOrganization} from "@shared/types";
import {useI18n} from "vue-i18n";
import FormPlus from "@/components/forms/FormPlus.vue";
import MainButton from "@/components/ui/MainButton.vue";
import {useApi} from "@/composition/api";
import {useValidators} from "@/composition/validators";
import {useFormatters} from "@/composition/formatters";
import FieldDate from "@/components/forms/FieldDate.vue";
import {useAuth} from "@/composition/snark-auth";
import LoadingContext from "@/components/ui/LoadingContext.vue";
import Iedit from "@/components/images/Iedit.vue";
import FieldCheckbox from "@/components/forms/FieldCheckbox.vue";
import {Routes} from "@/enums";
import router from "@/router";
import moment from "moment-timezone";

const {createAgency, updateAgency, getAgencyById, getSireneInfo} = useApi();
const {validateAgencyData} = useValidators();
const {getSelectOptions} = useFormatters();
const {currentUser} = useAuth();

const props = defineProps<{
	isCreation?: Boolean
}>();

const modifying = ref<boolean>(false)
const multipleAgent = ref<boolean>(false)

const errors = ref<any>(null);

const data = ref<IOrganization>({
	creatorId: currentUser.value!._id,
	type: currentUser.value!.organizationType,
	companyName: "",
	siret: "",
	address: "",
	creationDate: moment.now(),
});
const {t} = useI18n();

async function handleModifyClick() {

	data.value.creationDate = new Date(data.value.creationDate).getTime();

	const err = await validateAgencyData(data.value);
	console.log('err: ', err);
	if (err === null) {
		if (props.isCreation) {
			if (await createAgency(data.value)) {
				await router.push({name: Routes.appHome});
			}
			else {
				errors.value = 'creation';
			}
		}
		else {
			await updateAgency(data.value, currentUser.value!.organizationId || "undefined");
			modifying.value = false
		}

	}
	else {
		errors.value = err;
	}
}

async function loadAgency() {
	if (!props.isCreation) {
		if (currentUser.value!.organizationId != undefined) {
			data.value = await getAgencyById(currentUser.value!.organizationId);
		}
		else {
			console.log("current agency Id is undefined");
		}
	}
}

const siret = computed<string>({
	get(): string {
		return data.value.siret;
	},
	set(value: string) {
		if(data.value.siret !== value) {
			data.value.siret = value.replace(/\s/g, "");

			if(data.value.siret.length === 14) {
				lookForSiret(value)
			}
		}
	}
});

async function lookForSiret(siret: string) {
	try {
		const result = await getSireneInfo(siret);
		console.log("SIRET DATA: ", result);
		if(result.name) {
			data.value.companyName = result.name;
		}
		if(result.creationDate) {
			data.value.creationDate = result.creationDate;
		}
		if(result.address) {
			data.value.address = result.address.number + " " + result.address.street + " " + result.address.zipCode + " " + result.address.city;
		}
	}
	catch(err: any) {
		console.log("Error looking for siret: ", err);
	}
}
</script>

<template>
	<div :class="'OrganizationForm ' + data.type + (modifying ? ' modifying' : ' viewing')">
		<loading-context :loader="loadAgency">
			<form-plus :errors="errors" error-prefix="organization">
				<div class="groupField">
					<field-input :label="t(data.type + '.siret')" name="siret" v-model="siret"
								   :disabled="!isCreation && ! modifying"/>
					<div class="info" v-if="modifying">{{t(data.type + '.fillSiret')}}</div>
				</div>
				<div class="groupField">
				<field-input :label="t(data.type + '.name')" name="companyName" v-model="data.companyName"
							 :disabled="!isCreation && ! modifying"/>
				<field-date :label="t(data.type + '.creationDate')" name="creationDate" v-model="data.creationDate"
							:disabled="!isCreation && ! modifying"/>
				</div>
				<field-input :label="t(data.type + '.address')" name="address" v-model="data.address"
							 :disabled="!isCreation && ! modifying"/>
				<field-checkbox v-if="isCreation" name="multipleAgent" v-model="multipleAgent" :label-slug="data.type + '.multipleAgents'"/>
			</form-plus>

			<main-button v-if="isCreation" @click="handleModifyClick()">
				{{ t(data.type + '.saveButton') }}
			</main-button>
			<template v-else>
				<div v-if="modifying" class="buttonContainer">
					<main-button class="grey" @click="modifying = false">{{ t('global.cancel') }}</main-button>
					<main-button @click="handleModifyClick()">{{ t('player.modal.confirm') }}</main-button>
				</div>
				<main-button v-else @click="modifying = true">
					<Iedit/>
					{{ t('global.modify') }}
				</main-button>
			</template>
		</loading-context>
	</div>
</template>

<style lang="scss">
.OrganizationForm {

	.FormPlus {
		margin: 15px 0 30px;
	}

	.groupField {
		display: flex;
		gap: 30px;
		align-items: center;

		> * {
			flex: 1;
		}

		.info {
			color: $csearchGray;
		}
	}

	.MainButton {
		width: fit-content;
		margin: auto;
		padding: 15px;
	}

	.buttonContainer {
		display: flex;
		gap: 30px;
		justify-content: center;

		.MainButton {
			margin: 0;
		}
	}

	.FieldLayout {
		input {
			transition: padding-left 0.3s, border-color 0.3s;
		}
	}

	&.viewing {
		.FieldLayout {
			input {
				border-color: transparent;
				padding-left: 0;
			}
		}
	}
}
</style>