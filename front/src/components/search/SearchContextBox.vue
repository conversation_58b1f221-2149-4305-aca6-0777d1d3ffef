<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {SearchContext} from "@shared/crudTypes";

const props = defineProps<{
	context: SearchContext
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

</script>

<template>
	<div class="SearchContextBox">
		<div class="line">
			<div class="info">
				<label>Language:</label>
				<div class="value">{{context.language}}</div>
			</div>
			<div class="info">
				<label>Age:</label>
				<div class="value">{{context.minAge}} - {{context.maxAge}}</div>
			</div>
			<div class="info">
				<label>Gender:</label>
				<div class="value">{{context.gender ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Position:</label>
				<div class="value">{{context.position ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Last Season (min):</label>
				<div class="value">{{context.lastSeasonMinYear ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Salary Groups:</label>
				<div class="value">{{context.salaryGroups?.join(", ") ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Nationality:</label>
				<div class="value">{{context.nationality?.join(", ") ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Tiers categories:</label>
				<div class="value">{{context.tiersCategories?.join(", ") ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Tiers types:</label>
				<div class="value">{{context.tiersTypes?.join(", ") ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Matches count (min):</label>
				<div class="value">{{context.minMatchesCount ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Points (min):</label>
				<div class="value">{{context.minPoints ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Passes (min):</label>
				<div class="value">{{context.minPasses ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Rebounds (min):</label>
				<div class="value">{{context.minRebounds ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Blocks (min):</label>
				<div class="value">{{context.minBlocks ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Transitions points (min):</label>
				<div class="value">{{context.minTransitionPoints ?? '-'}}</div>
			</div>
			<div class="info">
				<label>Match points (min):</label>
				<div class="value">{{context.minMatchPoints ?? '-'}}</div>
			</div>
			<div class="info">
				<label>3-pt % (min):</label>
				<div class="value">{{context.min3PtPercentage ?? '-'}}</div>
			</div>
		</div>
		<div v-if="context.metrics && context.metrics.length > 0" class="line metrics">
			<div class="info" v-for="metric in context.metrics">
				<label>{{metric.name}}</label>
				<div class="value">{{metric.weight}}</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss">
.SearchContextBox {
	border: 1px solid $cborderGray;
	border-radius: 8px;
	padding: 10px;
	background-color: $cbackgroundGray;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

	display: flex;
	flex-direction: column;
	gap: 20px;

	.line {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
		gap: 10px 20px;

		.info {
			display: flex;
			gap: 5px;
			align-items: center;
			padding-left: 20px;
			border-left: 1px solid $cgrayBlue;

			&:first-child {
				padding-left: 0;
				border-left: none;
			}

			label {
				font-weight: $fmedium;
				color: $cgrayBlue;
			}

			.value {
				font-weight: bold;
				color: $cbrightBlue;
			}
		}
	}
}
</style>