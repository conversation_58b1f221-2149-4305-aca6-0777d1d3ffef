<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import {ISearchSessionDB} from "@shared/crudTypes";
import FormPlus from "@/components/forms/FormPlus.vue";
import {ref} from "vue";
import FieldInput from "@/components/forms/FieldInput.vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";

const {t} = useI18n()

const props = defineProps<{
	search: ISearchSessionDB
	errors: any
}>();
const emit = defineEmits<{
	(e: 'rename', name: string): void
}>();

const name = ref<string>(props.search.name);

function rename() {
	emit("rename", name.value);
}
</script>

<template>
	<modal class="ModalRenameSearch" :close-button="true" title-slug="search.rename.title">
		<form-plus :errors="errors" error-prefix="search">
			<field-input name="name" v-model="name" label-slug="search.fields.name.label" placeholder-slug="search.fields.name.placeholder" />
		</form-plus>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button @click="rename">{{ t('search.actions.rename') }}</main-button>
			</controls>
		</template>
	</modal>
</template>

<style lang="scss">
.ModalRenameSearch {
	.ModalBox {
		width: 410px;
	}
}
</style>