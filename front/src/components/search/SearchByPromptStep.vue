<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {IPlayerDB, ISearchSessionDB, SearchSessionStep} from "@shared/crudTypes";
import PlayerMiniCard from "@/components/cards/PlayerMiniCard.vue";
import {computed, ref} from "vue";
import PlayerFile from "@/components/player/PlayerFile.vue";
import Drawer from "@/components/ui/Drawer.vue";
import Irobot from "@/components/images/Irobot.vue";

const props = defineProps<{
	session: ISearchSessionDB
	step: SearchSessionStep
	index: number,
	lastStep: boolean
}>();
const emit = defineEmits<{
    (e: 'seeMore'): void
}>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

const selectedPlayer = ref<IPlayerDB|null>(null);

function selectPlayer(player: IPlayerDB) {
	//router.push({name: 'player', params: {playerId}});
	selectedPlayer.value = player;
}

const hasMorePlayers = computed<boolean>(() => {
	if(props.step.pagination) {
		return props.step.pagination.total > props.step.pagination.offset + props.step.pagination.limit;
	}

	return false;
});

function seeMore() {
	emit("seeMore");
}

</script>

<template>
	<div class="SearchByPromptStep">
<!--		<div class="bubble user step-query">-->
<!--			<ispeak/>-->
<!--			<span>{{step.query}}</span>-->
<!--		</div>-->

		<div class="bubble ai step-players">
			<irobot/>
			<div class="bubble-content">
				<div class="step-header">
					<div class="step-result" v-if="step.resultString">{{ step.resultString }}</div>
				</div>

				<div v-if="step.resultPlayerObjects && step.resultPlayerObjects.length > 0" class="players">
					<player-mini-card
						v-for="player in step.resultPlayerObjects"
						:key="player._id"
						:player="player"
						@click="selectPlayer(player)"
					/>
				</div>
				<div v-else-if="step.resultPlayers && step.resultPlayers.length > 0" class="empty-players">
					{{ t('search.playersNotLoaded') }}
				</div>
			</div>
		</div>
		<drawer :shown="!!selectedPlayer" @closed="selectedPlayer = null" v-slot="{close}">
			<PlayerFile :player="selectedPlayer!" @close="close"/>
		</drawer>
	</div>
</template>

<style lang="scss">
.SearchByPromptStep {
	display: flex;
	flex-direction: column;
	gap: 20px;

	.bubble {
		border: 1px solid $cborderGray;
		border-radius: 8px;
		padding: 20px;
		background-color: $cwhite;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

		display: flex;
		align-items: center;
		gap: 20px;

		.icon {
			flex: 0 0 50px;
			height: 50px;
		}

		span {
			display: block;
			font-size: 16px;
		}

		&.user {
			margin-right: 100px;
			flex-direction: row;
			color: $cbrightBlue;

			span {
				text-align: left;
			}

			.icon {
				color: $cbrightBlue;
			}
		}

		&.ai {
			margin-left: 100px;
			flex-direction: row-reverse;

			color: $cgrayBlue;

			span {
				text-align: right;
			}

			.icon {
				color: $cgrayBlue;
			}
		}

		&.step-players {
			align-items: flex-start;
		}
	}

	.bubble-content {
		flex: 1 1 0;
		.step-header {
			margin-bottom: 20px;

			.step-query {
				font-size: 16px;
				font-weight: bold;
				color: $cbrightBlue;

				i {
					font-style: italic;
					color: $cgrayBlue;
				}
			}

			.step-result {
				margin-top: 20px;
				color: $cgrayBlue;
				font-size: 16px;
			}
		}

		.players {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
			gap: 30px;
		}

		.empty-players {
			text-align: center;
			color: $cgrayBlue;
			font-size: 14px;
			padding: 20px;
			border: 1px dashed $cborderGray;
			border-radius: 8px;
		}
	}
}
</style>