<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useApi} from "@/composition/api";
import {useAnalytics} from "@/composition/analytics";
import {useFormatters} from "@/composition/formatters";
import {useRoute, useRouter} from "vue-router";
import {useAuth} from "@/composition/snark-auth";
import {ISearchSessionDB, IShortlistDB} from "@shared/crudTypes";
import Iplus from "@/components/images/Iplus.vue";
import {computed} from "vue";
import moment from "moment-timezone";

const props = defineProps<{
	search: ISearchSessionDB
}>();
// const emit = defineEmits<{
//     (e: 'click', value: any): void
// }>();

const router = useRouter();
const currentRoute = useRoute();
const {t} = useI18n();
const {sendEventAnalytics} = useAnalytics();
const {currentUser, isAuth} = useAuth();
const {} = useApi();
const {} = useFormatters();

</script>

<template>
	<div class="SearchCard">
		<h2 class="title">{{ search.name }}</h2>
		<div class="date">{{moment(search.updatedAt).format("DD/MM/YYYY HH:mm")}}</div>
	</div>
</template>

<style lang="scss">
.SearchCard {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10px;
	border: 1px solid $cborderGray;
	background-color: white;
	border-radius: 10px;
	padding: 15px;
	cursor: pointer;

	h2 {
		font-size: 14px;
		font-weight: $fbold;
		color: $cgrayBlue;
		margin-bottom: $gap2;
	}

	.pictures {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 10px;
		width: 100%;

		.picture {
			width: 100%;
			aspect-ratio: 1;
			border-radius: 10px;
			overflow: hidden;

			.dummy {
				width: 100%;
				height: 100%;
				background-color: $cborderGray;
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.more {
				width: 100%;
				height: 100%;

				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				gap: 10px;

				.icon {
					width: 20px;
					height: 20px;
					color: $cgrayBlue;
				}

				span {
					color: $cgrayBlue;
					font-size: 12px;
					font-weight: $fregular;
				}
			}
			}
	}
}
</style>