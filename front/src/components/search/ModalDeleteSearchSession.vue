<script setup lang="ts">

import Modal from "@/components/ui/Modal.vue";
import {useI18n} from "vue-i18n";
import {ISearchSessionDB} from "@shared/crudTypes";
import {computed, ref} from "vue";
import Controls from "@/components/ui/Controls.vue";
import MainButton from "@/components/ui/MainButton.vue";
import {useFormatters} from "@/composition/formatters";

const {t} = useI18n()

const props = defineProps<{
	searchSession: ISearchSessionDB
}>();
const emit = defineEmits<{
	(e: 'delete'): void
}>();

const {paragraphize} = useFormatters();

const name = ref<string>(props.searchSession.name);

const text = computed<string>(() => {
	return paragraphize(t('search.delete.text', [props.searchSession.name])) || "";
});

function deleteSearchSession() {
	emit("delete");
}
</script>

<template>
	<modal class="ModalDeleteSearchSession" :close-button="true" title-slug="search.delete.title">
		<div class="text" v-html="text"/>
		<template #footer="{close}">
			<controls>
				<main-button class="grey" @click="close">{{ t('global.cancel') }}</main-button>
				<main-button class="danger" @click="deleteSearchSession">{{ t('search.actions.delete') }}</main-button>
			</controls>
		</template>
	</modal>
</template>

<style lang="scss">
.ModalDeleteSearchSession {
	.ModalBox {
		width: 410px;

		.text {
			padding: 15px;
			border-radius: 10px;
			background-color: $cbackgroundGray;

			p {
				margin-top: 12px;
				color: $cdarktext;
				font-size: 16px;
				font-weight: 500;
				line-height: 1.3em;

				&:first-child {
					margin-top: 0;
				}

				b {
					font-weight: $fbold;
				}
			}
		}
	}
}
</style>