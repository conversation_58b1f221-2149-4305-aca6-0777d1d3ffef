<script setup lang="ts">
import { Scoring } from '@shared/types';
import Chart, { ChartDataset, LegendItem, TooltipItem } from 'chart.js/auto';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { COLORS } from '@/common';
import { getDynamicColor } from '@/common/helpers';

const REST_COLOR = '#f2f2f7';

const { data: chartData } = defineProps<{
	data: Scoring[];
}>();

const { t } = useI18n();

const chartRef = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart;

const data = {
	labels: [t('player.data.pointsPossession'), t('player.data.trueShooting')],
	datasets: chartData.map((d, index) => {
		return {
			label: d.label,
			data: [d.value, 100 - d.value],
			backgroundColor: [
				getDynamicColor(d.value, 100),
				REST_COLOR,
			],
			borderWidth: 0,
			cutout: '75%',
			radius: (100 - index * 10) + '%',
			borderRadius: 100,
		}
	}),
};

const options = {
	responsive: true,
	maintainAspectRatio: false,
	plugins: {
		legend: {
			position: 'bottom' as const,
			fullSize: false,
			title: {
				display: true,
				text: '',
				padding: {
					top: 20,
				},
			},
			labels: {
				usePointStyle: true,
				pointStyle: 'rectRounded',
				padding: 20,
				boxWidth: 8,
				boxHeight: 8,
				generateLabels: (chart: any) => {
					const datasets = chart.data.datasets;
					const legendLabels: LegendItem[] = [];

					datasets.forEach((dataset: ChartDataset<'doughnut', number[]>, datasetIndex: number) => {
						let color = '#000';
						if (Array.isArray(dataset.backgroundColor)) {
							color = dataset.backgroundColor[0] as string;
						} else if (typeof dataset.backgroundColor === 'string') {
							color = dataset.backgroundColor;
						}

						legendLabels.push({
							text: t('player.data.' + dataset.label),
								//datasetIndex === 0 ? t('player.data.pointsPossession') : t('player.data.trueShooting'),
							fillStyle: color,
							strokeStyle: color,
							datasetIndex,
							index: 1,
							hidden: false,
							lineCap: dataset.borderRadius ? 'round' : undefined,
							pointStyle: 'rectRounded',
						});
					});

					return legendLabels;
				},
			},
		},
		tooltip: {
			bodyFont: {
				weight: 500,
				size: 14,
				family: 'Inter',
			},
			titleFont: {
				size: 14,
				weight: 500,
				family: 'Inter',
			},
			titleColor: COLORS.CHARCOAL.LIGHT,
			backgroundColor: '#fff',
			borderColor: COLORS.GREY.LIGHT,
			borderWidth: 1,
			bodyColor: '#000',
			displayColors: false,
			padding: 12,
			caretSize: 6,
			filter: (tooltipItem: TooltipItem<'doughnut'>) => tooltipItem.dataIndex === 0,
			callbacks: {
				title: (ctx: TooltipItem<'doughnut'>[]) =>
					ctx.map((item) => {
						if (item.dataIndex === 1) {
							return '';
						}

						const label = t('player.data.' + item.label);
							//item.datasetIndex === 0 ? t('player.data.pointsPossession') : t('player.data.trueShooting');

						return label;
					}),
				label: (ctx: TooltipItem<'doughnut'>) => {
					if (ctx.dataIndex === 1) {
						return '';
					}

					return Number(ctx.raw).toFixed(1);
				},
			},
		},
	},
};

onMounted(() => {
	if (chartRef.value) {
		chartInstance = new Chart(chartRef.value, {
			type: 'doughnut',
			data,
			options,
		});
	}
});

onBeforeUnmount(() => {
	if (chartInstance) {
		chartInstance.destroy();
	}
});
</script>

<template>
	<canvas ref="chartRef" />
</template>
