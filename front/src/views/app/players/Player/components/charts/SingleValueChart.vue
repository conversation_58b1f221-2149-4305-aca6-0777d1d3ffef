<script setup lang="ts">
import Chart from 'chart.js/auto';
import { onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps<{
	score: number;
	color: string;
}>();

const chartRef = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart;

const chartData = {
	labels: ['score', 'rest'],
	datasets: [
		{
			data: [props.score, 100 - props.score],
			backgroundColor: [props.color, '#f1f2f8'],
			borderWidth: 0,
			hoverOffset: 0,
			borderRadius: 10,
			spacing: 5,
		},
	],
};

const chartOptions = {
	cutout: '80%',
	plugins: {
		legend: {
			display: false,
		},
		tooltip: {
			enabled: false,
		},
	},
};

const config = {
	type: 'doughnut' as const,
	data: chartData,
	options: chartOptions,
	plugins: [
		{
			id: 'centerText',
			beforeDraw(chart: any) {
				const { width } = chart;
				const { height } = chart;
				const ctx = chart.ctx;
				ctx.restore();

				ctx.font = '500 30px Inter';
				ctx.textBaseline = 'middle';
				ctx.fillStyle = '#1c1c1c';

				const text = `${props.score.toFixed(1)}`;
				const textX = Math.round((width - ctx.measureText(text).width) / 2);
				const textY = height / 2;

				ctx.fillText(text, textX, textY);
				ctx.save();
			},
		},
	],
};

onMounted(() => {
	if (chartRef.value) {
		chartInstance = new Chart(chartRef.value, config);
	}
});

onBeforeUnmount(() => {
	if (chartInstance) {
		chartInstance.destroy();
	}
});
</script>

<template>
	<div>
		<canvas ref="chartRef" />
	</div>
</template>
