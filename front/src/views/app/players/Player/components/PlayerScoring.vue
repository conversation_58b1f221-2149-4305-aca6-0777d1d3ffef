<script setup lang="ts">
import { Scoring } from '@shared/types';
import { useI18n } from 'vue-i18n';

import { Container, Typography } from '@/components/ui';

import { PlayerScoringChart } from './charts';

defineProps<{
	data: Scoring[];
}>();

const { t } = useI18n();
</script>

<template>
	<Container class="chart-container">
		<Typography variant="subtitle">{{ t('player.titles.playerScoring') }}</Typography>
		<Container class="chart-container__chart">
			<PlayerScoringChart :data="data" />
		</Container>
	</Container>
</template>

<style lang="scss" scoped>
.chart-container {
	width: 50%;
	gap: 16px;

	&__chart {
		margin: auto;
	}

	canvas {
		align-self: center;
	}
}
</style>
