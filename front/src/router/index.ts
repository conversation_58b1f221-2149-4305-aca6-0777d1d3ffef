import { LegalType } from '@shared/enums';
import { RouteR<PERSON>ord<PERSON>aw, createRouter, createWebHistory } from 'vue-router';

import { Routes } from '@/enums';
import Contact from '@/views/Contact.vue';
import Home from '@/views/Home.vue';
import Join from '@/views/Join.vue';
import LegalsPage from '@/views/LegalsPage.vue';
import AppHome from '@/views/app/AppHome.vue';
import History from '@/views/app/History.vue';
import IdentityCheckStatus from '@/views/app/IdentityCheckStatus.vue';
import Negotiation from '@/views/app/Negotiation.vue';
import NegotiationStatus from '@/views/app/NegotiationStatus.vue';
import Onboarding from '@/views/app/Onboarding.vue';
import PrivateApp from '@/views/app/PrivateApp.vue';
import Agency from '@/views/app/agency/Agency.vue';
import AIHome from '@/views/app/ai/AIHome.vue';
import AIPlayerSimilarity from '@/views/app/ai/AIPlayerSimilarity.vue';
import Club from '@/views/app/club/Club.vue';
import Dashboard from '@/views/app/dashboard.vue';
import Members from '@/views/app/members/Members.vue';
import AddPlayers from '@/views/app/players/AddPlayers.vue';
import Player from '@/views/app/players/Player/Player.vue';
import Players from '@/views/app/players/Players.vue';
import PlayersHome from '@/views/app/players/PlayersHome.vue';
import Profile from '@/views/app/profile/Profile.vue';
import SalariesEdition from '@/views/app/salaries/SalariesEdition.vue';
import SearchDetail from '@/views/app/search/SearchDetail.vue';
import SearchList from '@/views/app/search/SearchList.vue';
import ShortlistDetail from '@/views/app/shortlist/ShortlistDetail.vue';
import Shortlists from '@/views/app/shortlist/Shortlists.vue';
import ForgottenPassword from '@/views/auth/ForgottenPassword.vue';
import IdentityCheck from '@/views/auth/IdentityCheck.vue';
import LogAs from '@/views/auth/LogAs.vue';
import Login from '@/views/auth/Login.vue';
import Logout from '@/views/auth/Logout.vue';
import Register from '@/views/auth/Register.vue';
import ResetPassword from '@/views/auth/ResetPassword.vue';
import UserInvitation from '@/views/auth/UserInvitation.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: Routes.home,
    component: Home,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/contact',
    name: Routes.contact,
    component: Contact,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/join',
    name: Routes.join,
    component: Join,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/cgu',
    name: Routes.termsOfUse,
    component: LegalsPage,
    meta: {
      noAuthentication: true,
      type: LegalType.termsAndConditions,
    },
  },
  {
    path: '/politique-confidentialite',
    name: Routes.privacyPolicy,
    component: LegalsPage,
    meta: {
      noAuthentication: true,
      type: LegalType.privacyPolicy,
    },
  },
  {
    path: '/mentions-legales',
    name: Routes.legalNotices,
    component: LegalsPage,
    meta: {
      noAuthentication: true,
      type: LegalType.legalTerms,
    },
  },
  {
    path: '/cookies',
    name: Routes.cookies,
    component: LegalsPage,
    meta: {
      noAuthentication: true,
      type: LegalType.cookies,
    },
  },
  {
    path: '/connexion',
    name: Routes.login,
    component: Login,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/inscription',
    name: Routes.register,
    component: Register,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/invitation',
    name: Routes.invitation,
    component: UserInvitation,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/logas',
    name: Routes.logas,
    component: LogAs,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/motdepasse-oublie',
    name: Routes.forgottenPassword,
    component: ForgottenPassword,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/nouveau-motdepasse',
    name: Routes.resetPassword,
    component: ResetPassword,
    meta: {
      noAuthentication: true,
    },
  },
  {
    path: '/logout',
    name: Routes.logout,
    component: Logout,
  },
  {
    path: '/identity-check',
    name: Routes.identityCheck,
    component: IdentityCheck,
  },
  {
    path: '/identity-check-status',
    name: Routes.identityCheckStatus,
    component: IdentityCheckStatus,
  },
  {
    path: '/onboarding',
    name: Routes.onboarding,
    component: Onboarding,
  },
  {
    path: '/app',
    name: Routes.app,
    component: PrivateApp,
    redirect: { name: Routes.players },
    children: [
      {
        path: '',
        name: Routes.appHome,
        component: AppHome,
      },
      {
        path: 'profile',
        name: Routes.profile,
        component: Profile,
      },
      {
        path: 'dashboard',
        name: Routes.dashboard,
        component: Dashboard,
      },
      {
        path: 'negotiation-status',
        name: Routes.negotiationStatus,
        component: NegotiationStatus,
      },
      {
        path: 'negotiation',
        name: Routes.negotiation,
        component: Negotiation,
      },
      {
        path: 'agency',
        name: Routes.agency,
        component: Agency,
      },
      {
        path: 'club',
        name: Routes.club,
        component: Club,
      },
      {
        path: 'members',
        name: Routes.members,
        component: Members,
      },
      {
        path: 'historic',
        name: Routes.history,
        component: History,
      },
      {
        path: 'players',
        name: Routes.players,
        component: Players,
        redirect: { name: Routes.playersHome },
        children: [
          {
            path: '',
            name: Routes.playersHome,
            component: PlayersHome,
          },
          {
            path: 'add-players',
            name: Routes.addPlayers,
            component: AddPlayers,
          },
          {
            path: ':playerId',
            name: Routes.player,
            component: Player,
          },
        ],
      },
      {
        path: 'ai-home',
        name: Routes.ai,
        component: AIHome,
      },
      {
        path: 'ai',
        name: Routes.aiBase,
        redirect: { name: Routes.aiPlayerSimilarity },
        children: [
          {
            path: 'player-similarity/:playerId',
            name: Routes.aiPlayerSimilarity,
            component: AIPlayerSimilarity,
          },
        ],
      },
      {
        path: 'shortlist',
        name: Routes.shortlist,
        redirect: { name: Routes.shortlistList },
        children: [
          {
            path: '',
            name: Routes.shortlistList,
            component: Shortlists,
          },
          {
            path: ':shortlistId',
            name: Routes.shortlistDetail,
            component: ShortlistDetail,
          },
        ],
      },
      {
        path: 'search',
        name: Routes.search,
        redirect: { name: Routes.searchList },
        children: [
          {
            path: '',
            name: Routes.searchList,
            component: SearchList,
          },
          {
            path: ':searchId',
            name: Routes.searchDetail,
            component: SearchDetail,
          },
        ],
      },
      {
        path: 'salaries',
        name: Routes.salaries,
        component: SalariesEdition,
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
