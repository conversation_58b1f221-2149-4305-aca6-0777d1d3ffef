#!/bin/zsh
set -euo pipefail


if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <update-seasons|update-tournaments|tournament-player-stats|tournament-dirty-manager>"
    exit 1
fi
if [[ $1 == "update-seasons" ]]; then
    DIR_NAME=jobs/update-seasons
    JOB_NAME=playerlynk-update-seasons
    WORKSPACE_NAME=@playerlynk/job-update-seasons
    COPY_DATA=false
elif [[ $1 == "update-tournaments" ]]; then
    DIR_NAME=jobs/update-tournaments
    JOB_NAME=playerlynk-update-tournaments
    WORKSPACE_NAME=@playerlynk/job-update-tournaments
    COPY_DATA=true
elif [[ $1 == "tournament-player-stats" ]]; then
    DIR_NAME=jobs/tournament-player-stats
    JOB_NAME=playerlynk-tournament-player-stats
    WORKSPACE_NAME=@playerlynk/job-tournament-player-stats
    COPY_DATA=false
elif [[ $1 == "tournament-dirty-manager" ]]; then
    DIR_NAME=jobs/tournament-dirty-manager
    JOB_NAME=playerlynk-tournament-dirty-manager
    WORKSPACE_NAME=@playerlynk/job-tournament-dirty-manager
    COPY_DATA=false
else
    echo "Unknown job: $1"
    exit 1
fi

# Configuration
PROJECT_ID=snappy-cosine-439512-i3
DOCKER_FILE=job.dockerfile
DOCKER_CONTEXT=.
REGION=europe-west1
VPC_CONNECTOR=cloud-run-connector-west1

REPOSITORY_HOST=${REGION}-docker.pkg.dev
IMAGE_NAME=${REPOSITORY_HOST}/${PROJECT_ID}/cloud-run-source-deploy/${JOB_NAME}
DATE=$(date +%Y_%m_%d_%H_%M_%S)

echo "Building project locally..."
yarn install
lerna run --scope ${WORKSPACE_NAME} build

echo "Building Docker image..."
BUILD_HASH=$(git rev-parse --short HEAD)-${DATE}

docker buildx build -f ${DOCKER_FILE} \
    --platform linux/amd64 \
    -t ${IMAGE_NAME}:latest \
    -t ${IMAGE_NAME}:"${BUILD_HASH}" \
    --build-arg PACKAGE_NAME="${WORKSPACE_NAME}" \
    --build-arg DIR_NAME="${DIR_NAME}" \
    --build-arg COPY_DATA="${COPY_DATA}" \
    ${DOCKER_CONTEXT}
gcloud auth configure-docker ${REPOSITORY_HOST}
docker push --all-tags ${IMAGE_NAME}

# Deploy Cloud Run Job
echo "Deploying Cloud Run Job..."
gcloud run jobs deploy ${JOB_NAME} --image ${IMAGE_NAME} --project ${PROJECT_ID} --region ${REGION} \
    --memory 512Mi \
    --cpu 1 \
    --set-env-vars NODE_ENV=production \
    --vpc-connector ${VPC_CONNECTOR} \
    --max-retries 0 \
    --task-timeout 24h

echo "✅ Deployment complete!"