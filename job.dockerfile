# Use the official lightweight Node.js runtime image
FROM node:22-alpine

ARG PACKAGE_NAME
ARG DIR_NAME
ARG COPY_DATA=false

# Create app directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock lerna.json ./
COPY .npmrc.prod .npmrc
COPY shared/package.json ./shared/
COPY commons/package.json ./commons/
COPY $DIR_NAME/package.json ./$DIR_NAME/

# Install only production deps for my-service and its monorepo deps
RUN yarn install --production

COPY shared/dist ./shared/dist/
COPY commons/dist ./commons/dist/
COPY $DIR_NAME/dist ./$DIR_NAME/dist/
COPY $DIR_NAME/data ./$DIR_NAME/data/

# Set environment
ENV NODE_ENV=production

# Run the job
WORKDIR /app/$DIR_NAME
CMD ["node", "dist/index.js"]