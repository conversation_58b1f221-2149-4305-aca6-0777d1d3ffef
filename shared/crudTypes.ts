// noinspection JSUnusedGlobalSymbols

import type {timestamp, objectId, DB, price} from "./baseTypes"
import {ContactType, LegalType, NotificationType, NotificationLevel, NotificationStatus, OrganizationType, Role, UserStatus, GenderCategory, BonusType, AvailabilitiesOption, PlayerStatus, TiersType, TiersCategory, CompetitionType, TournamentScope, AgeCategory, Gender, PlayerPosition, SearchStepType, SearchMissingData, MissingData} from "./enums";

export interface IBouser {
	username: string
	password: string
	token?: string
	tokenDate?: timestamp
}
export interface IBouserDB extends IBouser, DB {}

export interface IContact {
	name: string
	message: string
	email?: string
	phone?: string
	type: ContactType
	data?: any
}
export interface IContactDB extends IContact, DB {}

export interface LegalImage {
	path: string
	size?: number
	mimetype?: string
}

export interface LegalBlock {
	blockName: string
	text?: string
	image?: LegalImage
}

export interface ILegals {
	type: LegalType
	blocks: LegalBlock[]
}
export interface ILegalsDB extends ILegals, DB {}

export interface INotificationItem {
	userId?: objectId
}

export interface INotification {
	title?: string
	text?: string
	link?: string
	type: NotificationType
	items: INotificationItem
	data: any
	level: NotificationLevel
	status: NotificationStatus
	terminationTimestamp?: timestamp
}
export interface INotificationDB extends INotification, DB {}

export interface Person {
	firstname: string
	lastname: string
}

export interface Picture {
	path: string
	thumbnail?: string
	mimetype?: string
}

export interface FeaturesSchema {
	playerSimilarity?: boolean
}

export interface IUser extends Person {
	organizationId: objectId
	organization?: IOrganizationDB
	organizationType: OrganizationType
	email: string
	password?: string
	picture?: Picture
	role?: Role
	termsAccepted?: boolean
	phone: string
	marketingAccepted?: boolean
	fibaLicense?: boolean
	federalLicense?: boolean
	status: UserStatus
	preferredLanguage?: string
	features?: FeaturesSchema
	token?: string
	tokenExpire?: timestamp
	resetToken?: string
	resetTokenExpire?: timestamp
}
export interface IUserDB extends IUser, DB {}

export interface OrganizationSquadPlayer {
	playerId: string
	playerName: string
	playerNumber?: string
}

export interface IOrganization {
	creatorId: objectId
	companyName: string
	type: OrganizationType
	hudlTeamId?: string
	siret: string
	address: string
	creationDate: timestamp
	shortName?: string
	countryId?: string
	countryName?: string
	gender?: GenderCategory
	currentSquad?: OrganizationSquadPlayer[]
}
export interface IOrganizationDB extends IOrganization, DB {}

export interface IContract extends Person {
	agencyId: objectId
	playerId: objectId
	salary: number
	bonus: number
	benefits?: BonusType
}
export interface IContractDB extends IContract, DB {}

export interface StatSchema {
	points?: number
	twoPoints?: number
	threePoints?: number
	freeThrows?: number
	rebounds?: number
	assists?: number
	steal?: number
	blocks?: number
}

export interface IPlayer extends Person {
	invitationToken?: string
	external_id: number
	external_birthday?: string
	external_club_number: number
	external_club_team_id: number
	external_club_team_name: string
	external_country1_id?: number
	external_country1_name?: string
	external_firstname: string
	external_gender_id: number
	external_gender_name: string
	external_hand_id: number
	external_hand_name: string
	external_height?: number
	external_lastname: string
	external_national_number?: number
	external_national_team_id?: number
	external_national_team_name?: string
	external_photo: string
	external_position1_id?: number
	external_position1_name?: string
	external_position2_id?: number
	external_position2_name?: string
	email: string
	availabilities?: AvailabilitiesOption
	phoneNumber?: string
	salary?: price
	salaryGroup?: string
	computedSalaryGroup?: string
	accommodation?: boolean
	carFunction?: boolean
	agencyId?: objectId
	status?: PlayerStatus
	stat?: StatSchema
	slug?: string
	lastSeasonId?: string
	lastSeasonName?: string
	lastSeasonStats?: any
	lastSeasonYear?: number
	score?: number
	lastSeasonMatchesCount?: number
	lastSeasonAgeCategory?: string
	lastSeasonCompetitionType?: string
	lastSeasonGenderCategory?: string
	lastSeasonScope?: string
	lastSeasonTiersCategory?: string
	lastSeasonTiersType?: string
	similarPlayers?: objectId[]
}
export interface IPlayerDB extends IPlayer, DB {}

export interface INegotiation {
	agencyId: objectId
	teamId: objectId
	playerId: objectId
	date: timestamp
}
export interface INegotiationDB extends INegotiation, DB {}

export interface IMatch {
	hudlId: string
	date: timestamp
	tournamentHudlId: string
	tournamentName: string
	seasonHudlId: string
	seasonName: string
	team1HudlId: string
	team1Name: string
	team2HudlId: string
	team2Name: string
	team1Score: string
	team2Score: string
	statusHuldId: string
	statusName: string
	matchName: string
}
export interface IMatchDB extends IMatch, DB {}

export interface ITournament {
	hudlId: string
	name: string
	countryCode?: string
	countryName?: string
	tiersType?: TiersType
	tiersCategory?: TiersCategory
	competitionType?: CompetitionType
	scope?: TournamentScope
	ageCateogry?: AgeCategory
	genderCategory?: GenderCategory
}
export interface ITournamentDB extends ITournament, DB {}

export interface ISeason {
	hudlId: string
	name: string
	start: timestamp
	end: timestamp
}
export interface ISeasonDB extends ISeason, DB {}

export interface StatValue {
	id: string
	name: string
	value: number
}

export interface IPlayerStatsByMatch {
	external_id: string
	external_match_id: string
	external_player_id: string
	external_statistics: StatValue[]
	external_ts: string
}
export interface IPlayerStatsByMatchDB extends IPlayerStatsByMatch, DB {}

export interface IPlayerVector {
	playerId: objectId
	player?: IPlayerDB	// populated
	gender: Gender
	lastYear: number
	age?: number
	positions?: string[]
	salaryGroup?: string
	nationality?: string
	lastSeasonTiersCategory?: string
	lastSeasonTiersType?: string
	lastSeasonMatchesCount?: number
	vector: any
}
export interface IPlayerVectorDB extends IPlayerVector, DB {}

export interface IShortlist {
	userId: objectId
	user?: IUserDB	// populated
	name: string
	players: objectId[]
	playerObjects?: IPlayerDB[]	// populated
	sharedWith?: objectId[]
	sharedWithUsers?: IUserDB[]	// populated
}
export interface IShortlistDB extends IShortlist, DB {}

export interface WeightedMetric {
	name: string
	weight: number
}

export interface SearchContext {
	language: string
	minAge?: number
	maxAge?: number
	gender?: GenderCategory
	salaryGroups?: string[]
	lastSeasonMinYear?: number
	position?: PlayerPosition
	nationality?: string[]
	tiersCategories?: string[]
	tiersTypes?: string[]
	minMatchesCount?: number
	minPoints?: number
	minPasses?: number
	minRebounds?: number
	minBlocks?: number
	minTransitionPoints?: number
	minMatchPoints?: number
	min3PtPercentage?: number
	metrics?: WeightedMetric[]
}

export interface Pagination {
	offset: number
	limit: number
	total: number
}

export interface SearchSessionStep {
	timestamp: timestamp
	type: SearchStepType
	context: SearchContext
	query?: string
	promptCompletion?: boolean
	playerId?: objectId
	player?: IPlayerDB	// populated
	resultString?: string
	resultPlayers?: objectId[]
	resultPlayerObjects?: IPlayerDB[]	// populated
	pagination?: Pagination
	question?: string
	missingData?: SearchMissingData
}

export interface ISearchSession {
	userId: objectId
	user?: IUserDB	// populated
	language?: string
	name: string
	firstQuery: string
	context: SearchContext
	steps: SearchSessionStep[]
	sharedWith?: objectId[]
	sharedWithUsers?: IUserDB[]	// populated
}
export interface ISearchSessionDB extends ISearchSession, DB {}

export interface IMissingData {
	type: MissingData
	name: string
	externalId?: string
	entityId?: objectId
	data?: any
	treatedAt?: timestamp
}
export interface IMissingDataDB extends IMissingData, DB {}
