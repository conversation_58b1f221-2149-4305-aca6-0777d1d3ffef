export enum DBState {
	deleted = "deleted",
	draft = "draft"
}

export enum DBCollection {
	bouser = "bouser",
	contact = "contact",
	legals = "legals",
	newsletter = "newsletter",
	notification = "notification",
	user = "user",
	player = "player",
	organization = "organization",
	contract = "contract",
	negotiation = "negotiation",
	match = "match",
	playerStatsByMatch = "player_stats_by_match",
	playerStatsBySeason = "player_stats_by_season",
	global_stats = "global_stats",
	player_vector = "player_vector",

	tournament = "tournament",
	season = "season",

	shortlist = "shortlist",
	searchSession = "searchSession",

	hudlCountry = "hudl_country",
	hudlTournament = "hudl_tournament",
	hudlSeason = "hudl_season",
	hudlTeam = "hudl_team",
	hudlTeamMatchStat = "hudl_team_match_stat",
	hudlPlayerMatchStat = "hudl_player_match_stat",
	hudlPlayerSeasonStat = "hudl_player_season_stat",

	teamSquad = "team_squad",
	dirtyTournamentSeason = "tournament_season_dirty",

	missingData = "missing_data"
}

export enum PlayerStatus {
	invited = "invited",
	represented = "represented",
	free = "free"
}

export enum AvailabilitiesOption {
	available = "available",
	signed = "signed",
	ongoingNegotiations = "ongoingNegotiations"
}

export enum AnalyticsType {
	page = "page",
	action = "action"
}

export enum ContactType {
	site = "site",
	getOwnData = "getOwnData",
	deleteAccount = "deleteAccount"
}

export enum AgencyType {
	test1 = "test1",
	test2 = "test2",
	test3 = "test3"
}

export enum Role {
	admin = "admin",
	member = "member",
	superadmin = "superadmin"
}

export enum OrganizationType {
	club = "club",
	agency = "agency"
}

export enum UserStatus {
	waiting = "waiting",
	accepted = "accepted"
}

export enum BonusType {
	test1 = "test1",
	test2 = "test2",
	test3 = "test3"
}

export enum NotificationMode {
	email = "email",
	push = "push",
	database = "database"
}

export enum NotificationLevel {
	info = 'info',
	warning = 'warning',
	alert = 'alert'
}

export enum NotificationStatus {
	unread = 'unread',
	read = 'read',
	terminated = 'terminated',
	archived = "archived"
}

export enum NegotiationStatus {
	waiting = 'waiting',
	sign = 'sign',
}

export enum NotificationType {
	userForgotPassword = "userForgotPassword",
	contactFromSite = "contactFromSite",
	personalDataRequest = "personalDataRequest",
	invitationPlayer="invitationPlayer",
	confirmationPlayer="confirmationPlayer",
	declinationPlayer = "declinationPlayer",
	invitationNewUser = "invitationNewUser",
	invitationExistingUser = "invitationExistingUser",
	missingData = "missingData"
}

export enum LegalType {
	termsAndConditions = "termsAndConditions",
	privacyPolicy = "privacyPolicy",
	legalTerms = "legalTerms",
	cookies = "cookies"
}

export enum SortOrder {
	asc = "asc",
	desc = "desc",
	random = "random",
	none = "none"
}

export enum Gender {
	M = "M",
	F = "F"
}

export enum SearchStepType {
	byPlayer = "byPlayer",
	byPrompt = "byPrompt",

	dummy = "dummy",
	byName = "byName",
	needMoreContext = "needMoreContext",
	computing = "computing",						// c'est un step avec une information mais on doit continuer le processus automatiquement
	information = "information"
}

export enum SearchMissingData {
	age = "age",
	gender = "gender",
	position = "position",
	salary = "salary",
	lastSeason = "lastSeason"
}

export enum CompetitionType {
	league = "league",						// : Championnat régulier (saison longue)
	cup = "cup",							// : Tournoi à élimination directe
	tournament = "tournament",				// : Événement court avec plusieurs équipes
	exhibition = "exhibition",				// : Matchs amicaux et événements spéciaux
}

export enum TournamentScope {
	global = "global",	// (FIBA World Cup, Jeux Olympiques, etc.)
	international = "international",	// ?? peut être = à global
	continental = "continental",		// (Euroleague, Champions League, AfroBasket, etc.)
	national = "national",				// (LNB Pro A, ACB, NBA, etc.)
	regional = "regional"				// (Compétitions locales et sous-nationales)
}

export enum AgeCategory {
	senior = "senior",	// (Toutes les catégories adultes professionnelles)
	u23 = "u23",		// (Espoirs, Next Gen, etc.)
	u22 = "u22",
	u21 = "u21",
	u20 = "u20",		// (Compétitions FIBA, championnats nationaux U20, etc.)
	u19 = "u19",
	u18 = "u18",		// (Championnats juniors, compétitions FIBA U18, etc.)
	u17 = "u17",
	u16 = "u16",		// (Tournois cadets, compétitions U16 nationales et internationales, etc.)
	u15 = "u15",
	u14 = "u14",		// (Tournois jeunes, compétitions de clubs de formation, etc.)
	u13 = "u13",
	u12 = "u12",
	u11 = "u11",
	u10 = "u10",
	youth = "youth"		// (Autres compétitions de développement sans âge précis)
}

export enum GenderCategory {
	male = "male",
	female = "female",
	mixed = "mixed"
}

export enum TiersType {
	professional = "professional",
	university = "university",
	youth = "youth"
}

export enum TiersCategory {
	// pro type
	s = "s",	// : Compétitions élites mondiales (NBA, Euroleague, FIBA World Cup, Jeux Olympiques, WNBA, etc.)
	a = "a",	// : Principales ligues continentales/nationales (ACB, LNB Pro A, EuroCup, etc.)
	b = "b",	// : Ligues professionnelles solides (FIBA Champions League, ligues nationales de haut niveau, etc.)
	c = "c",	// : Ligues de développement professionnel (Deuxièmes divisions, petites ligues nationales, etc.)
	d = "d",	// : Ligues semi-professionnelles (Ligues régionales, petites fédérations, etc.)
	e = "e",	// : Ligues amateurs/locales (Compétitions locales, ligues amateurs, tournois scolaires, etc.)

	// university type
	college1 = "college1",	// : NCAA Division I (Plus haut niveau universitaire, voie vers le basket pro)
	college2 = "college2",	// : NCAA Division II (Niveau intermédiaire, quelques débouchés professionnels)
	college3 = "college3",	// : NCAA Division III (Compétition amateur, peu d'opportunités professionnelles)
	college4 = "college4",	// : NJCAA / Autres compétitions universitaires (Ligues de développement universitaire)

	// youth type
	youth1 = "youth1",	// : Compétitions internationales jeunes (FIBA U18/U19, tournois mondiaux, etc.)
	youth2 = "youth2",	// : Ligues nationales jeunes (Championnats nationaux U18, U16, etc.)
	youth3 = "youth3",	// : Ligues de développement jeunes (Ligues régionales et locales, tournois de formation, etc.)
}

export const TiersMapping = {
	[TiersCategory.s]: TiersType.professional,
	[TiersCategory.a]: TiersType.professional,
	[TiersCategory.b]: TiersType.professional,
	[TiersCategory.c]: TiersType.professional,
	[TiersCategory.d]: TiersType.professional,
	[TiersCategory.e]: TiersType.professional,

	[TiersCategory.college1]: TiersType.university,
	[TiersCategory.college2]: TiersType.university,
	[TiersCategory.college3]: TiersType.university,
	[TiersCategory.college4]: TiersType.university,

	[TiersCategory.youth1]: TiersType.youth,
	[TiersCategory.youth2]: TiersType.youth,
	[TiersCategory.youth3]: TiersType.youth,
}

export const salaryGroups = [
	2000,
	4000,
	6000,
	9000,
	12000,
	15000,
	20000,
	30000,
	50000,
	100000,
	200000,
	500000,
];

export enum PlayerPosition {
	Guard = "Guard",
	Forward = "Forward",
	Center = "Center"
}

export enum MissingData {
	country = "country",
	tournament_enrichment = "tournament_enrichment"
}