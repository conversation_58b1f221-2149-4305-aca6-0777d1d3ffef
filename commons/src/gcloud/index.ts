import {gcloudConfig} from "../commons";
import {SecretManagerServiceClient} from "@google-cloud/secret-manager";
import {ExecutionsClient, protos, JobsClient} from '@google-cloud/run';

export async function gcGetSecretValue(secretName: string, secretVersion: string = "latest"): Promise<string|null> {
	const name = `projects/${gcloudConfig.projectId}/secrets/${secretName}/versions/${secretVersion}`;

	const secretmanagerClient = new SecretManagerServiceClient();
	const response = await secretmanagerClient.accessSecretVersion({name});
	if (response.length > 0) {
		return response[0]?.payload?.data?.toString() || null;
	}
	else {
		return null;
	}
}

async function isJobRunning(jobName: string): Promise<boolean> {
	const parent = `projects/${gcloudConfig.projectId}/locations/${gcloudConfig.region}/jobs/${jobName}`;

	const runClient = new ExecutionsClient();
	const [executions] = await runClient.listExecutions({parent});
	if (!executions || executions.length === 0) return false;

	const latest: protos.google.cloud.run.v2.IExecution = executions[0];
	const conditions = latest.conditions ?? [];

	const started = conditions.find(c => c.type === 'Started' && c.state === 'CONDITION_SUCCEEDED');
	const completed = conditions.find(c => c.type === 'Completed' && c.state === 'CONDITION_SUCCEEDED');

	return !!(started && !completed);
}

async function runJob(jobName: string, args: string[]): Promise<void> {
	const parent = `projects/${gcloudConfig.projectId}/locations/${gcloudConfig.region}/jobs/${jobName}`;

	const client = new JobsClient();

	client.runJob({name: parent, overrides: {containerOverrides: [{args}]}}, {
		timeout: 24 * 60 * 60 * 1000,
	});
}

	runClient.
	// Build the execution request with argument overrides
	const request: protos.google.cloud.run.v2.CreateJobRequestICreateExecutionRequest = {
		parent,
		execution: {
			overrides: {
				containerOverrides: [
					{
						args, // This overrides the container arguments
					},
				],
			},
		},
	};
