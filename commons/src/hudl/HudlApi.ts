import {RateLimitedFetcher} from "./RateLImitedFetcher";
import {
	HudlAggregatedStatParam, HudlApiConfiguration,
	HudlMatch,
	HudlMatchLineups, HudlPlayerInfo,
	HudlPlayerStats,
	HudlSeason, HudlSimpleTeam, HudlSquadPlayer, HudlStatParam,
	HudlTeam,
	HudlTeamStats,
	HudlTournament
} from "./hudlTypes";
import {DBCollection, timestamp} from "@playerlynk/shared";
import moment from "moment-timezone";
import {IDatabase} from "@snark/mongodb-operator";

export enum HudlTemplate {
	allTournaments = 262,
	allSeasons = 204,
	tournamentTeams = 261,
	tournamentStandings = 259,
	teamInfo = 264,
	teamSquad = 27,
	playerInfo = 263,
	playerCareer = 265,
	matches = 26,
	matchInfo = 24,
	matchLineUps = 25,
	playerMatchStatistics = 28,
	teamMatchStatistics = 29,
	playerStatsOneByOne = 30,
	teamStatsOneByOne = 31,
	playerStatsAggr = 185,
	playerStatsAggrMatches = 186,
	teamStatsAggr = 318,
	teamStatsAggrMatches = 319,
	matchEvents = 337,
	videos = "video"
}

// noinspection JSUnusedGlobalSymbols
export class HudlApi {
	private readonly id: string
	private readonly key: string
	private readonly apiUrl: string
	private db: IDatabase
	private readonly lang_id: string
	private readonly format: string

	private fetcher: RateLimitedFetcher;

	constructor(config: HudlApiConfiguration, debug: boolean = true) {
		this.id = config.id;
		this.key = config.key;
		this.apiUrl = config.apiUrl;
		this.db = config.db;

		this.lang_id = "1";
		this.format = "json"

		this.fetcher = new RateLimitedFetcher(config.rateLimitation, debug);
	}

	public async fetch(template: HudlTemplate, data: any): Promise<any> {
		return await this.fetcher.fetch(this.apiUrl, {
			id: this.id,
			key: this.key,
			lang_id: this.lang_id,
			format: this.format,
			tpl: template,
			...data
		});
	}

	public async getCountryMap(): Promise<{[key: number]: string}> {
		const all = await this.db.find(DBCollection.hudlCountry, {}, {projection: {hudlId: 1, name: 1}});
		const countries: any = {};
		for(let doc of all) {
			countries[doc.hudlId] = doc.name;
		}
		return countries;
	}

	public async getAllTournaments(): Promise<HudlTournament[]> {
		const result = await this.fetch(HudlTemplate.allTournaments, {});
		return result.data.tournament;
	}

	public async getAllSeasons(): Promise<HudlSeason[]> {
		const result = await this.fetch(HudlTemplate.allSeasons, {});
		return result.data.tournament;
	}

	public async getCurrentSeasons(): Promise<HudlSeason[]> {
		const now = moment.now();
		return this.db.find(DBCollection.hudlSeason, {start: {$lte: now}, end: {$gte: now}}, {sort: {start: 1, end: 1}});
	}

	public async getMatches(from: timestamp, to: timestamp): Promise<HudlMatch[]> {
		try {
			const formattedStart = moment(from).format("YYYY-MM-DD");
			const formattedEnd = moment(to).format("YYYY-MM-DD");
			const result = await this.fetch(HudlTemplate.matches, {
				date_start: formattedStart,
				date_end: formattedEnd
			});
			return result.data.row;
		}
		catch(err: any) {
			let errorToThrow: any = null;
			try {
				const idx = err.indexOf("{");
				const jsonErr = JSON.parse(err.substring(idx));
				if (jsonErr.status === "Error" && jsonErr.error_code === 7) {
					return [];
				}
				else {
					errorToThrow = jsonErr;
				}
			}
			catch (parseErr: any) {
				errorToThrow = err;
			}

			throw errorToThrow;
		}
	}

	public async getMatchInfo(id: string): Promise<HudlMatch> {
		const result = await this.fetch(HudlTemplate.matchInfo, {
			match_id: id
		});
		return result.data.match_info[0];
	}

	public async getMatchLineUps(id: string): Promise<HudlMatchLineups> {
		const result = await this.fetch(HudlTemplate.matchLineUps, {
			match_id: id
		});
		return result.data;
	}

	public async getTeamInfo(id: string): Promise<HudlTeam> {
		const result = await this.fetch(HudlTemplate.teamInfo, {
			team_id: id
		});
		return result.data.row[0];
	}

	public async getPlayerStatsFromMatch(id: string): Promise<HudlPlayerStats[]> {
		const result = await this.fetch(HudlTemplate.playerMatchStatistics, {
			match_id: id
		});
		let players: HudlPlayerStats[] = [];
		const teams = result.data.team;
		for (const team of teams) {
			players.push(...(team.player ?? []));
		}
		return players;
	}

	public async getTeamStatsFromMatch(id: string): Promise<HudlTeamStats[]> {
		const result = await this.fetch(HudlTemplate.teamMatchStatistics, {
			match_id: id
		});
		let teams: {[key: string]: HudlTeamStats} = {};

		for (const row of result.data.row) {
			if (teams[row.team_id] == undefined) {
				teams[row.team_id] = {
					id: row.team_id,
					name: row.team_name,
					param: []
				};
			}

			teams[row.team_id].param.push({
				id: row.param_id,
				name: row.param_name,
				value: row.value
			});
		}
		return Object.values(teams);
	}

	public async getTeamSquad(id: number, tournamentId: number, seasonId: number): Promise<HudlSquadPlayer[]> {
		const result = await this.fetch(HudlTemplate.teamSquad, {
			team_id: id,
			tournament_id: tournamentId,
			season_id: seasonId
		});
		return result.data.row;
	}

	public async getTournamentTeams(id: number, seasonId: number): Promise<HudlSimpleTeam[]> {
		const result = await this.fetch(HudlTemplate.tournamentTeams, {
			tournament_id: id,
			season_id: seasonId
		});
		return result.data.row;
	}

	public async getPlayerAggregatedStats(id: string, seasonId: number, tournamentId?: number): Promise<HudlAggregatedStatParam[]> {
		const result = await this.fetch(HudlTemplate.playerStatsAggr, {
			player_id: id,
			tournament_id: tournamentId,
			season_id: seasonId
		});
		return result.data.param;
	}

	public async getTournamentMatches(tournamentId: number, seasonId: number): Promise<HudlMatch[]> {
		const result = await this.fetch(HudlTemplate.matches, {
			tournament_id: tournamentId,
			season_id: seasonId
		});
		return result.data.row;
	}

	public async getPlayerInfo(id: string): Promise<HudlPlayerInfo|null> {
		try {
			const result = await this.fetch(HudlTemplate.playerInfo, {
				player_id: id
			});
			return result?.data?.row?.length > 0 ? result.data.row[0] : null;
		}
		catch(err: any) {
			console.error(`Error on getPlayerInfo: ${err}`);
			return null;
		}
	}

	public transformParamsIntoStats(params: HudlStatParam[]): any {
		let stats: any = {};
		for(const param of params) {
			let value: any = parseFloat(param.value);
			if(isNaN(value)) {
				value = param.value;
			}
			stats[param.name] = value;
		}
		return stats;
	}

}