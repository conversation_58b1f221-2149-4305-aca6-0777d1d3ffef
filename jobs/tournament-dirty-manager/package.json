{"name": "@playerlynk/job-tournament-dirty-manager", "private": true, "version": "0.0.1", "description": "playerlynk-job-tournament-dirty-manager", "main": "dist/index.js", "scripts": {"dev": "node dist/index.js", "build": "tsc", "build-watch": "tsc -w"}, "author": "Startomatic", "dependencies": {"@google-cloud/run": "^3.0.0", "@playerlynk/commons": "*", "@playerlynk/shared": "*", "@snark/mongodb-operator": "^3.3.2", "moment-timezone": "^0.6.0", "mongodb": "^6.18.0"}, "devDependencies": {"@types/node": "^22.17.2", "@types/source-map-support": "^0", "source-map-support": "^0.5.21", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2"}}