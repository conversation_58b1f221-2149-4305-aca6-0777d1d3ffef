import {connectToDatabase, createHudlApi} from "@playerlynk/commons";
import {DBCollection, IDirtyTournament} from "@playerlynk/shared";

/**
 * Get the next dirty tournament season and trigger the tournament-player-stats job to compute it.
 */
async function manageDirtyTournament(): Promise<void> {
	try {
		console.log("🤖 MANAGE DIRTY TOURNAMENT");

		console.log("🤖 VERIFY NO OTHER RUNNING JOB");
		isJobRunning("playerlynk-tournament-player-stats");

		const db = await connectToDatabase(true);
		const api = await createHudlApi(db);

		console.log("🤖 STARTING MANAGING DIRTY TOURNAMENT");

		const dirtyTournament: IDirtyTournament|null = await db.findOne(DBCollection.dirtyTournamentSeason, {}, {sort: {createdAt: 1}});
		if(dirtyTournament) {
			console.log(`Next dirty tournament found: ${dirtyTournament.tournamentId} on season ${dirtyTournament.seasonId}`);
		}
		else {
			console.log("No more dirty `tournament. We stop the process for now.");
		}

		console.log("✅  END OF MANAGING DIRTY TOURNAMENT");
	}
	catch(err) {
		console.error("❌ ERROR ON MANAGING DIRTY TOURNAMENT");
		console.error(err);
	}
}

// -----------------------------------------------------------------------------
// Main

(async function() {
	try {
		await manageDirtyTournament();
		process.exit(0);
	}
	catch(err) {
		console.error("GLOBAL ERROR: ");
		console.error(err);
		process.exit(-1);
	}
})();


async function isJobRunning({project, location, jobName}: JobCheckOptions): Promise<boolean> {
	const parent = `projects/${project}/locations/${location}/jobs/${jobName}`;

	const [executions] = await runClient.listExecutions({parent});
	if (!executions || executions.length === 0) return false;

	const latest: protos.google.cloud.run.v2.IExecution = executions[0];
	const conditions = latest.status?.conditions ?? [];

	const started = conditions.find(c => c.type === 'Started' && c.state === 'CONDITION_SUCCEEDED');
	const completed = conditions.find(c => c.type === 'Completed' && c.state === 'CONDITION_SUCCEEDED');

	return !!(started && !completed);
}