import {
	HudlAggregated<PERSON>tatParam,
	HudlMatchLineupTeam,
	HudlSimplePlayer
} from "@playerlynk/commons";
import {HudlPlayerSeasonEnrichedStats, PlayerWork, TeamSquadPlayer, TournamentStat} from "./types";
import {overviewConfig} from "./config";

export function lineUpWellFormatted(lineUps: HudlMatchLineupTeam[]): boolean {
	return lineUps.length > 0 && lineUps[0].lineup.length > 0 && lineUps[0].lineup[0].main.length > 0 && !!lineUps[0].lineup[0].main[0].player;
}

export function getPlayersFromLineUps(lineUps: HudlMatchLineupTeam[]): HudlSimplePlayer[] {
	return lineUps[0].lineup[0].main[0].player;
}

export function addPlayers(playersToUpdate: Record<string, PlayerWork>, teamPlayers: Record<number, TeamSquadPlayer[]>, players: HudlSimplePlayer[], teamId: number) {
	for(const player of players) {
		playersToUpdate[player.id] = {
			player,
			teamId,
			param:[]
		};
	}
	if(teamPlayers[teamId] === undefined) {
		teamPlayers[teamId] = players.map(p => {
			return {
				playerId: p.id,
				playerName: p.firstname + " " + p.lastname,
			};
		});
	}
	else {
		for(const player of players) {
			if(!teamPlayers[teamId].find(p => p.playerId === player.id)) {
				teamPlayers[teamId].push({
					playerId: player.id,
					playerName: player.firstname + " " + player.lastname,
				});
			}
		}
	}
}

export function computePlaying40Minutes(player: HudlPlayerSeasonEnrichedStats): number|undefined {
	const p = player.param.find(p => p.name === "Seconds on the court");
	if (p) {
		// console.log(`playing 40 minutes: ${p.value_sum} -> ${p.value_sum / 2400.0}`);
		return p.value_sum / 2400.0;	// 2400 secondes = 40 * 60 = 40 minutes
	}
	return undefined;
}

export function isFieldPercentage(name: string): boolean {
	return name.includes("%") || name.toLowerCase().includes("percentage");
}

export function getNormalizedValue(p: HudlAggregatedStatParam, playing40Minutes: number): number {
	if(isFieldPercentage(p.name)) {
		return p.value_sum;
	}
	else {
		return p.value_sum / playing40Minutes;
	}
}

export function getTournamentAvgValue(name: string, data: TournamentStat): number {
	if(data && data.count > 0) {
		return (data.avg_sum / data.count * 100 - data.min) / (data.max - data.min);
	}
	else {
		return 0;
	}
}

function getTournamentAvgDisplayedValue(name: string, data: TournamentStat): number {
	if(data && data.count > 0) {
		return data.avg_sum / data.count * 100;
	}
	else {
		return 0;
	}
}

export function addPlayerToTournamentStats(tournamentStats: Record<string, TournamentStat>, player: HudlPlayerSeasonEnrichedStats) {
	if(player.playing40Minutes !== undefined) {
		for (const p of player.param) {
			let value = getNormalizedValue(p, player.playing40Minutes);
			let keyname = p.name;
			if (keyname === "Seconds on the court") {
				keyname = "avg_minutes";
				value = p.value_avg / 60.0;
			}

			if (tournamentStats[keyname] === undefined) {
				tournamentStats[keyname] = {
					name: keyname,
					min: value,
					max: value,
					count: 1,
					avg_sum: value
				};
			}
			else {
				tournamentStats[keyname].min = Math.min(tournamentStats[keyname].min, value);
				tournamentStats[keyname].max = Math.max(tournamentStats[keyname].max, value);
				tournamentStats[keyname].count++;
				tournamentStats[keyname].avg_sum += value;
			}
		}
	}
}

export function computePlayerNormalizedStats(player: HudlPlayerSeasonEnrichedStats, tournamentStats: Record<string, TournamentStat>): Record<string, number> {
	const normalizedStats : Record<string, number> = {};
	for (const p of player.param) {
		let value = getNormalizedValue(p, player.playing40Minutes!);	// this methid is called only if playing40Minutes is defined
		let keyname = p.name;
		if(keyname === "Seconds on the court") {
			keyname = "avg_minutes";
			value = p.value_avg / 60.0;
		}

		// compute percentile
		if(tournamentStats[keyname] !== undefined) {
			const percentile = (value - tournamentStats[keyname].min) / (tournamentStats[keyname].max - tournamentStats[keyname].min);
			normalizedStats[keyname] = percentile;
			// console.log(`--- ${keyname}: ${value} -> (${tournamentStats[keyname].min}-${tournamentStats[keyname].max}) = ${percentile}`);
		}
		else {
			console.log(`--- no tournament stats for ${keyname}`);
		}
	}

	return normalizedStats;
}

export function computeOverview(player: HudlPlayerSeasonEnrichedStats) {
	let overview = [];
	for(const config of overviewConfig) {
		console.log(`OVERVIEW ${config.labelSlug}...`);
		let value = 0;
		let count = 0;
		for(const stat of config.stats) {
			if(player.normalizedParam[stat] !== undefined) {
				console.log(`   ${stat} = ${player.normalizedParam[stat]}`);
				value += player.normalizedParam[stat] ?? 0;
				count++;
			}
			else {
				console.log(`--- no player normalized stats for ${stat}`);
			}
		}
		console.log(`=> end of overview ${config.labelSlug}: `, count === 0 ? NaN : value / count);
		player.overview.push({
			labelSlug: config.labelSlug,
			value: count === 0 ? NaN : value / count * 100
		});
	}

	return overview;
}