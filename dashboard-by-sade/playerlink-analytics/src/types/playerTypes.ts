export interface PlayerCategory {
  primary: string[];    // Main role/position
  secondary: string[];  // Secondary characteristics
  playstyle: string[]; // How they play the game
  specialty: string[]; // Specific skills they excel at
}

export const PlayerTypes = {
  scoring: {
    pure: 'Pure Scorer',
    threeLevel: 'Three-Level Scorer',
    sharpshooter: 'Sharpshooter',
    slasher: 'Slasher',
    volumeShooter: 'Volume Shooter',
    clutch: 'Clutch Performer',
    postScorer: 'Post Scorer',
    isoSpecialist: 'Iso Specialist'
  },
  playmaking: {
    floorGeneral: 'Floor General',
    facilitator: 'Facilitator',
    pickAndRollMaestro: 'Pick-and-Roll Maestro',
    passFirst: 'Pass-First Guard',
    creativePlaymaker: 'Creative Playmaker',
    tempoSetter: 'Tempo Setter'
  },
  defensive: {
    lockdown: 'Lockdown Defender',
    rimProtector: 'Rim Protector',
    perimeterDefender: 'Perimeter Defender',
    defensiveAnchor: 'Defensive Anchor',
    stealSpecialist: 'Steal Specialist',
    reboundingMachine: 'Rebounding Machine'
  },
  athletic: {
    highFlyer: 'High-Flyer',
    speedster: 'Speed<PERSON>',
    agileFinisher: 'Agile Finisher',
    powerPlayer: 'Power Player',
    enduranceBeast: 'Endurance Beast'
  },
  specialist: {
    threeAndD: '3-and-D Player',
    stretchBig: 'Stretch Big',
    hustler: 'Hustler',
    glueGuy: 'Glue Guy',
    sixthMan: 'Sixth Man'
  },
  intangibles: {
    teamLeader: 'Team Leader',
    mentor: 'Mentor',
    playoffPerformer: 'Playoff Performer',
    ironMan: 'Iron Man',
    wildcard: 'Wildcard'
  }
} as const;