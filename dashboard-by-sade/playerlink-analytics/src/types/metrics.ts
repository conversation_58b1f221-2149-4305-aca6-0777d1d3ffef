export interface DNAScores {
  scoring: number;
  playmaking: number;
  defense: number;
  impact: number;
}

export interface Metric {
  label: string;
  value: number;
  isPercentage: boolean;
}

export type PlayerPosition = 'guard' | 'wing' | 'big';

export interface Metrics {
  dnaScores: DNAScores;
  scoring: Metric[];
  playmaking: Metric[];
  defense: Metric[];
  efficiency: Metric[];
  position: PlayerPosition;
}