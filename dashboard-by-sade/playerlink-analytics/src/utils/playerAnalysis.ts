import { Metrics } from '../types/metrics';
import { PlayerTypes } from '../types/playerTypes';
import { getPersonalityTraits, getPlayStyle } from './analysisHelpers';

const determinePlayerIdentity = (metrics: Metrics): string[] => {
  const identity = new Set<string>();
  const { dnaScores } = metrics;
  const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value || 0;
  const ppp = metrics.scoring.find(m => m.label === 'Points Per Possession')?.value || 0;
  const assistRatio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value || 0;
  const usageRate = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value || 0;

  // Scoring Identity
  if (ts > 65 && ppp > 1.1) {
    identity.add(PlayerTypes.scoring.threeLevel);
  } else if (ts > 60) {
    identity.add(PlayerTypes.scoring.pure);
  }

  // Playmaking Identity
  if (assistRatio > 3.5) {
    identity.add(PlayerTypes.playmaking.floorGeneral);
  } else if (assistRatio > 2.5) {
    identity.add(PlayerTypes.playmaking.facilitator);
  }

  // Role Identity
  if (usageRate > 28) {
    identity.add(PlayerTypes.scoring.volumeShooter);
  } else if (dnaScores.defense > 70) {
    identity.add(PlayerTypes.defensive.lockdown);
  }

  // Add personality traits and playstyle
  getPersonalityTraits(metrics).forEach(trait => identity.add(trait));
  getPlayStyle(metrics).forEach(style => identity.add(style));

  return Array.from(identity);
};

const generatePlayerKeywords = (metrics: Metrics): string[] => {
  const keywords = [];
  const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value || 0;
  const assistRatio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value || 0;
  const usageRate = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value || 0;
  const netRating = metrics.efficiency.find(m => m.label === 'Net Rating')?.value || 0;

  // Offensive Identity
  if (ts > 65) keywords.push('Elite Shooter');
  else if (ts > 58) keywords.push('Efficient Scorer');
  
  // Playmaking Identity
  if (assistRatio > 3.5) keywords.push('Master Facilitator');
  else if (assistRatio > 2.5) keywords.push('Skilled Playmaker');
  
  // Role Identity
  if (usageRate > 28) keywords.push('Primary Option');
  else if (usageRate > 22) keywords.push('Key Contributor');
  else keywords.push('Role Player');
  
  // Impact Identity
  if (netRating > 10) keywords.push('Game Changer');
  else if (netRating > 5) keywords.push('Impact Player');

  return keywords;
};

const generateDetailedStrengths = (metrics: Metrics): string[] => {
  const strengths = [];
  const { position } = metrics;
  const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value || 0;
  const ppp = metrics.scoring.find(m => m.label === 'Points Per Possession')?.value || 0;
  const assistRatio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value || 0;
  const pointsCreated = metrics.playmaking.find(m => m.label === 'Points Created')?.value || 0;
  const usageRate = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value || 0;
  const netRating = metrics.efficiency.find(m => m.label === 'Net Rating')?.value || 0;
  const defRating = metrics.defense.find(m => m.label === 'Defensive Rating')?.value || 0;
  const steals = metrics.defense.find(m => m.label === 'Steals Per Game')?.value || 0;
  const blocks = metrics.defense.find(m => m.label === 'Blocks Per Game')?.value || 0;
  const contested = metrics.defense.find(m => m.label === 'Contested Shots')?.value || 0;

  // Position-specific scoring analysis
  if (position === 'guard') {
    if (ts > 65 && ppp > 1.1) {
      strengths.push('Elite perimeter scoring threat with exceptional efficiency in pick-and-roll situations');
      strengths.push('Superior shot creation ability from all three levels');
    } else if (ts > 60 && usageRate > 25) {
      strengths.push('Efficient lead guard who maintains shooting effectiveness despite high usage');
      strengths.push('Creates quality looks off the dribble in half-court sets');
    }
  } else if (position === 'wing') {
    if (ts > 62 && ppp > 1.0) {
      strengths.push('Versatile scoring wing who excels in catch-and-shoot and transition opportunities');
      strengths.push('Effective scorer from multiple spots on the floor');
    } else if (ts > 58 && usageRate > 20) {
      strengths.push('Reliable secondary scoring option who moves well without the ball');
      strengths.push('Capitalizes on spot-up opportunities and cuts');
    }
  } else {
    if (ts > 65) {
      strengths.push('Highly efficient interior finisher who maximizes scoring opportunities');
      strengths.push('Strong presence in the paint with excellent shot selection');
    } else if (ts > 60) {
      strengths.push('Reliable post scorer who creates high-percentage looks near the basket');
    }
  }

  // Position-specific playmaking analysis
  if (position === 'guard') {
    if (assistRatio > 3.5 && pointsCreated > 15) {
      strengths.push('Elite floor general who consistently creates high-quality looks for teammates');
      strengths.push('Exceptional decision-maker in pick-and-roll and transition situations');
    } else if (assistRatio > 2.5) {
      strengths.push('Effective lead playmaker who maintains positive assist-to-turnover ratio');
      strengths.push('Shows good court vision and passing instincts');
    }
  } else if (position === 'wing') {
    if (assistRatio > 2.5) {
      strengths.push('Above-average secondary playmaker for the position');
      strengths.push('Effectively finds cutters and spot-up shooters from the wing');
    }
  } else if (assistRatio > 2.0) {
    strengths.push('Strong passing big man who facilitates well from the post and elbow');
    strengths.push('Makes good reads when facing double teams');
  }

  // Position-specific defensive analysis
  if (position === 'guard') {
    if (defRating < 105 && steals > 1.5) {
      strengths.push('Disruptive on-ball defender who generates turnovers through active hands');
      strengths.push('Effectively navigates screens and contains dribble penetration');
    } else if (defRating < 110 && contested > 4) {
      strengths.push('Solid perimeter defender who consistently challenges shots');
    }
  } else if (position === 'wing') {
    if (defRating < 105 && (steals > 1.2 || blocks > 0.8)) {
      strengths.push('Versatile defender capable of guarding multiple positions');
      strengths.push('Makes timely help rotations and disrupts passing lanes');
    }
  } else {
    if (blocks > 1.5 || contested > 6) {
      strengths.push('Imposing rim protector who alters shots and protects the paint');
      strengths.push('Strong defensive anchor in pick-and-roll coverage');
    }
  }

  // Position-specific impact analysis
  if (netRating > 10) {
    if (position === 'guard') {
      strengths.push('High-impact lead guard who elevates team offensive efficiency');
    } else if (position === 'wing') {
      strengths.push('Significant two-way impact through versatile play on both ends');
    } else {
      strengths.push('Dominant interior presence who controls the game on both ends');
    }
  }

  return strengths;
};

const generateDetailedWeaknesses = (metrics: Metrics): string[] => {
  const weaknesses = [];
  const { position } = metrics;
  const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value || 0;
  const assistRatio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value || 0;
  const usageRate = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value || 0;
  const defRating = metrics.defense.find(m => m.label === 'Defensive Rating')?.value || 0;
  const steals = metrics.defense.find(m => m.label === 'Steals Per Game')?.value || 0;
  const blocks = metrics.defense.find(m => m.label === 'Blocks Per Game')?.value || 0;
  const contested = metrics.defense.find(m => m.label === 'Contested Shots')?.value || 0;

  // Position-specific scoring development
  if (position === 'guard') {
    if (ts < 52 && usageRate > 20) {
      weaknesses.push('Needs to improve shot selection in pick-and-roll situations');
      weaknesses.push('Can develop more consistent three-point shooting off the dribble');
    } else if (ts < 55) {
      weaknesses.push('Should focus on finishing through contact and drawing fouls');
    }
  } else if (position === 'wing') {
    if (ts < 54) {
      weaknesses.push('Can improve catch-and-shoot efficiency from three-point range');
      weaknesses.push('Should develop more consistent mid-range game off movement');
    }
  } else {
    if (ts < 58) {
      weaknesses.push('Can improve finishing efficiency around the rim');
      weaknesses.push('Should develop more reliable post moves and counter-moves');
    }
  }

  // Position-specific playmaking development
  if (position === 'guard') {
    if (assistRatio < 2.0) {
      weaknesses.push('Needs to improve decision-making in pick-and-roll situations');
      weaknesses.push('Should work on advanced passing reads and ball security under pressure');
    }
  } else if (position === 'wing') {
    if (assistRatio < 1.5 && usageRate > 18) {
      weaknesses.push('Can improve playmaking reads when driving from the wing');
      weaknesses.push('Should develop better passing skills in drive-and-kick situations');
    }
  } else if (assistRatio < 1.0 && usageRate > 15) {
    weaknesses.push('Can improve passing out of the post and reading double teams');
    weaknesses.push('Should work on facilitating from the elbow and high post');
  }

  // Position-specific defensive development
  if (position === 'guard') {
    if (defRating > 110 && steals < 1.0) {
      weaknesses.push('Needs to improve on-ball containment and screen navigation');
      weaknesses.push('Should focus on staying in front of quick guards and contesting shots');
    }
  } else if (position === 'wing') {
    if (defRating > 108 && contested < 4) {
      weaknesses.push('Can improve defensive positioning and closeout technique');
      weaknesses.push('Should work on defending multiple positions more effectively');
    }
  } else {
    if (defRating > 108 && blocks < 1.0) {
      weaknesses.push('Needs to improve rim protection timing and vertical contests');
      weaknesses.push('Should develop better pick-and-roll defensive positioning');
    }
  }

  return weaknesses;
};

export const analyzePlayer = (metrics: Metrics) => {
  return {
    playerType: determinePlayerIdentity(metrics),
    keywords: generatePlayerKeywords(metrics),
    strengths: generateDetailedStrengths(metrics),
    weaknesses: generateDetailedWeaknesses(metrics)
  };
};