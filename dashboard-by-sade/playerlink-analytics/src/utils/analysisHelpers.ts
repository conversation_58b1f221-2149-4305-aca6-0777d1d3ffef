import { Metrics } from '../types/metrics';

export const getPersonalityTraits = (metrics: Metrics): string[] => {
  const traits = [];
  const { dnaScores } = metrics;
  const usageRate = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value || 0;
  const netRating = metrics.efficiency.find(m => m.label === 'Net Rating')?.value || 0;

  // Determine personality traits based on metrics
  if (usageRate > 25 && netRating > 5) traits.push('Natural Leader');
  if (dnaScores.playmaking > 70) traits.push('Court Maestro');
  if (dnaScores.defense > 70) traits.push('Defensive Anchor');
  if (dnaScores.scoring > 75) traits.push('Scoring Virtuoso');
  if (netRating > 10) traits.push('Game Changer');
  
  return traits;
};

export const getPlayStyle = (metrics: Metrics): string[] => {
  const styles = [];
  const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value || 0;
  const assistRatio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value || 0;

  if (ts > 60) styles.push('Efficient Marksman');
  if (assistRatio > 3) styles.push('Elite Facilitator');
  if (metrics.dnaScores.defense > 65) styles.push('Defensive Specialist');
  
  return styles;
};