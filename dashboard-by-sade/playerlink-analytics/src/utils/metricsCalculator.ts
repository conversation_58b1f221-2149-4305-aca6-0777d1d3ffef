import { Metrics, PlayerPosition } from '../types/metrics';

const POSITION_BENCHMARKS = {
  guard: {
    trueShootingPct: 0.58,
    efgPct: 0.53,
    pointsPerPoss: 1.0,
    usagePct: 0.25,
    threePtPct: 0.38,
    twoPtPct: 0.48,
    ftPct: 0.85,
    contestedPct: 0.42,
    defensiveRating: 110,
    oppPointsPer100: 110,
    stealsPerGame: 1.5,
    blocksPerGame: 0.3,
    contestedShotsPerGame: 6.0,
    impact: {
      netRating: 6.0,
      plusMinus: 5.0,
      offRating: 115,
      defRating: 110,
      foulsDrawn: 4.0
    }
  },
  wing: {
    trueShootingPct: 0.60,
    efgPct: 0.54,
    pointsPerPoss: 0.95,
    usagePct: 0.23,
    threePtPct: 0.36,
    twoPtPct: 0.52,
    ftPct: 0.80,
    contestedPct: 0.45,
    defensiveRating: 108,
    oppPointsPer100: 108,
    stealsPerGame: 1.2,
    blocksPerGame: 0.7,
    contestedShotsPerGame: 7.0,
    impact: {
      netRating: 5.0,
      plusMinus: 4.0,
      offRating: 113,
      defRating: 108,
      foulsDrawn: 3.5
    }
  },
  big: {
    trueShootingPct: 0.62,
    efgPct: 0.58,
    pointsPerPoss: 0.90,
    usagePct: 0.20,
    threePtPct: 0.33,
    twoPtPct: 0.60,
    ftPct: 0.75,
    contestedPct: 0.50,
    defensiveRating: 106,
    oppPointsPer100: 106,
    stealsPerGame: 0.8,
    blocksPerGame: 1.5,
    contestedShotsPerGame: 8.0,
    impact: {
      netRating: 4.0,
      plusMinus: 3.0,
      offRating: 110,
      defRating: 106,
      foulsDrawn: 3.0
    }
  }
};

const POSITION_WEIGHTS = {
  guard: {
    efficiency: 0.30,
    volume: 0.30,
    versatility: 0.25,
    creation: 0.15
  },
  wing: {
    efficiency: 0.35,
    volume: 0.25,
    versatility: 0.20,
    creation: 0.20
  },
  big: {
    efficiency: 0.40,
    volume: 0.20,
    versatility: 0.15,
    creation: 0.25
  }
};

const getNum = (val: any): number => {
  if (!val || val === '-') return 0;
  if (typeof val === 'number') return val;
  const parsed = parseFloat(val.toString().replace(/[^0-9.-]/g, ''));
  return isNaN(parsed) ? 0 : parsed;
};

const parsePercentage = (val: any): number => {
  const num = getNum(val);
  return num > 1 ? num / 100 : num;
};

const calculateScoringScore = (stats: any, position: PlayerPosition): number => {
  const benchmarks = POSITION_BENCHMARKS[position];
  const weights = POSITION_WEIGHTS[position];
  
  const normalize = (value: number, benchmark: number) => Math.min(100, (value / benchmark) * 100);

  const ts = parsePercentage(stats['True shooting percentage']);
  const efg = parsePercentage(stats['Effective field goal percentage']);
  const ppp = getNum(stats["Points per player's possession"]);
  const usage = parsePercentage(stats['Usage Percentage']);
  const threePt = parsePercentage(stats['3-pt field goals, %']);
  const twoPt = parsePercentage(stats['2-pt field goals, %']);
  const ft = parsePercentage(stats['Free throws, %']);
  const contested = parsePercentage(stats['Contested field goals, %']);

  const efficiency = (
    0.5 * normalize(ts, benchmarks.trueShootingPct) +
    0.5 * normalize(efg, benchmarks.efgPct)
  );

  const volume = (
    0.6 * normalize(ppp, benchmarks.pointsPerPoss) +
    0.4 * normalize(usage, benchmarks.usagePct)
  );

  const versatility = position === 'big' ? (
    0.2 * normalize(threePt, benchmarks.threePtPct) +
    0.5 * normalize(twoPt, benchmarks.twoPtPct) +
    0.3 * normalize(ft, benchmarks.ftPct)
  ) : position === 'guard' ? (
    0.45 * normalize(threePt, benchmarks.threePtPct) +
    0.25 * normalize(twoPt, benchmarks.twoPtPct) +
    0.30 * normalize(ft, benchmarks.ftPct)
  ) : (
    0.35 * normalize(threePt, benchmarks.threePtPct) +
    0.35 * normalize(twoPt, benchmarks.twoPtPct) +
    0.30 * normalize(ft, benchmarks.ftPct)
  );

  const creation = (
    0.7 * normalize(contested, benchmarks.contestedPct) +
    0.3 * normalize(usage, benchmarks.usagePct)
  );

  return Math.min(100, Math.max(0,
    weights.efficiency * efficiency +
    weights.volume * volume +
    weights.versatility * versatility +
    weights.creation * creation
  ));
};

const calculatePlaymaking = (stats: any, position: PlayerPosition): number => {
  const usage = parsePercentage(stats['Usage Percentage']);
  const turnovers = getNum(stats['Turnovers']);
  const baseAssists = usage > 0.4 ? 3.5 : 2.3;
  const assists = stats['Assists'] === '-' ? baseAssists : getNum(stats['Assists']);
  const assistRatio = turnovers > 0 ? assists / turnovers : assists;
  const pointsCreated = stats['Points off assists'] === '-' ? 
    assists * 2.5 : getNum(stats['Points off assists']);

  let baseScore = Math.min(100,
    (assistRatio * 10) +
    (pointsCreated * 0.7) +
    (assists * 6) +
    (usage * 25)
  );

  return position === 'guard' ? baseScore * 1.1 :
         position === 'wing' ? baseScore :
         baseScore * 0.8;
};

const calculateDefenseScore = (stats: any, position: PlayerPosition): number => {
  const benchmarks = POSITION_BENCHMARKS[position];
  
  const normalizeInverse = (value: number, benchmark: number) => 
    Math.min(100, Math.max(0, 100 * (benchmark / Math.max(value, benchmark/2))));
  
  const normalize = (value: number, benchmark: number) => 
    Math.min(100, Math.max(0, (value / benchmark) * 100));

  const defRating = getNum(stats['Defensive rating']);
  const contestedShots = getNum(stats['Contested shots per game']);
  const oppPoints = getNum(stats["Opponent's points with player"]);
  const oppPossessions = getNum(stats['Opponent possessions played']);
  const oppPointsPer100 = oppPossessions > 0 ? (oppPoints / oppPossessions) * 100 : 0;
  const steals = getNum(stats['Steals per game']);
  const blocks = getNum(stats['Blocks per game']);

  if (position === 'guard') {
    return Math.min(100, Math.max(0,
      0.45 * normalizeInverse(defRating, benchmarks.defensiveRating) +
      0.35 * (
        0.6 * normalize(steals, benchmarks.stealsPerGame) +
        0.4 * normalize(contestedShots, benchmarks.contestedShotsPerGame)
      ) +
      0.20 * normalizeInverse(oppPointsPer100, benchmarks.oppPointsPer100)
    ));
  }
  else if (position === 'wing') {
    return Math.min(100, Math.max(0,
      0.35 * normalizeInverse(defRating, benchmarks.defensiveRating) +
      0.40 * (
        0.4 * normalize(steals, benchmarks.stealsPerGame) +
        0.3 * normalize(blocks, benchmarks.blocksPerGame) +
        0.3 * normalize(contestedShots, benchmarks.contestedShotsPerGame)
      ) +
      0.25 * normalizeInverse(oppPointsPer100, benchmarks.oppPointsPer100)
    ));
  }
  else {
    return Math.min(100, Math.max(0,
      0.30 * normalizeInverse(defRating, benchmarks.defensiveRating) +
      0.45 * (
        0.6 * normalize(blocks, benchmarks.blocksPerGame) +
        0.4 * normalize(contestedShots, benchmarks.contestedShotsPerGame)
      ) +
      0.25 * normalizeInverse(oppPointsPer100, benchmarks.oppPointsPer100)
    ));
  }
};

const calculateImpactScore = (stats: any, position: PlayerPosition): number => {
  const benchmarks = POSITION_BENCHMARKS[position].impact;
  
  const normalize = (value: number, benchmark: number) => 
    Math.min(100, Math.max(0, (value / benchmark) * 100));
  
  const normalizeInverse = (value: number, benchmark: number) => 
    Math.min(100, Math.max(0, 100 * (benchmark / Math.max(value, benchmark/2))));

  const netRating = getNum(stats['Net rating']);
  const plusMinus = getNum(stats['Plus/Minus']);
  const defRating = getNum(stats['Defensive rating']);
  const offRating = getNum(stats['Offensive rating']);
  const foulsDrawn = getNum(stats['Fouls drawn']);
  const usage = parsePercentage(stats['Usage Percentage']);
  const possessions = getNum(stats['Number of player\'s possessions']);

  const directImpact = (
    0.4 * (((netRating + 15) / 30) * 100) +
    0.3 * (((plusMinus + 10) / 20) * 100) +
    0.3 * normalize(foulsDrawn, benchmarks.foulsDrawn)
  );

  const efficiencyImpact = (
    0.5 * normalize(offRating, benchmarks.offRating) +
    0.5 * normalizeInverse(defRating, benchmarks.defRating)
  );

  const volumeImpact = (
    0.5 * normalize(usage * 100, 25) +
    0.5 * (possessions / 40 * 100)
  );

  if (position === 'guard') {
    return Math.min(100, Math.max(0,
      0.35 * directImpact +
      0.35 * efficiencyImpact +
      0.30 * volumeImpact
    ));
  } 
  else if (position === 'wing') {
    return Math.min(100, Math.max(0,
      0.40 * directImpact +
      0.35 * efficiencyImpact +
      0.25 * volumeImpact
    ));
  }
  else {
    return Math.min(100, Math.max(0,
      0.45 * directImpact +
      0.35 * efficiencyImpact +
      0.20 * volumeImpact
    ));
  }
};

export const calculateMetrics = (data: any[], position: PlayerPosition): Metrics => {
  // Aggregate stats across all games
  const aggregatedStats = data.reduce((acc, game) => {
    const minutes = getNum(game['Minutes']?.split(':')[0]) || 0;
    if (minutes === 0) return acc;

    // Accumulate stats
    acc.games++;
    acc.assists += getNum(game['Assists']) || 0;
    acc.turnovers += getNum(game['Turnovers']) || 0;
    acc.pointsCreated += getNum(game['Points off assists']) || 0;
    acc.blocks += getNum(game['Blocks']) || 0;
    acc.steals += getNum(game['Steals']) || 0;
    acc.contested += getNum(game['Contested shots']) || 0;
    acc.foulsDrawn += getNum(game['Fouls drawn']) || 0;
    
    // Accumulate shooting stats for percentages
    acc.fgMade += getNum(game['Field goals made']) || 0;
    acc.fgAttempts += getNum(game['Field goals attempted']) || 0;
    acc.threePtMade += getNum(game['3-pt field goals made']) || 0;
    acc.threePtAttempts += getNum(game['3-pt field goals attempted']) || 0;
    acc.ftMade += getNum(game['Free throws made']) || 0;
    acc.ftAttempts += getNum(game['Free throws attempted']) || 0;
    
    return acc;
  }, {
    games: 0,
    assists: 0,
    turnovers: 0,
    pointsCreated: 0,
    blocks: 0,
    steals: 0,
    contested: 0,
    foulsDrawn: 0,
    fgMade: 0,
    fgAttempts: 0,
    threePtMade: 0,
    threePtAttempts: 0,
    ftMade: 0,
    ftAttempts: 0
  });

  // Calculate per game averages
  const perGame = {
    assists: aggregatedStats.games > 0 ? aggregatedStats.assists / aggregatedStats.games : 0,
    blocks: aggregatedStats.games > 0 ? aggregatedStats.blocks / aggregatedStats.games : 0,
    steals: aggregatedStats.games > 0 ? aggregatedStats.steals / aggregatedStats.games : 0,
    contested: aggregatedStats.games > 0 ? aggregatedStats.contested / aggregatedStats.games : 0,
    pointsCreated: aggregatedStats.games > 0 ? aggregatedStats.pointsCreated / aggregatedStats.games : 0
  };

  // Calculate percentages
  const percentages = {
    fg: aggregatedStats.fgAttempts > 0 ? aggregatedStats.fgMade / aggregatedStats.fgAttempts : 0,
    threePt: aggregatedStats.threePtAttempts > 0 ? aggregatedStats.threePtMade / aggregatedStats.threePtAttempts : 0,
    ft: aggregatedStats.ftAttempts > 0 ? aggregatedStats.ftMade / aggregatedStats.ftAttempts : 0
  };

  // Get latest game stats for ratings
  const latestGame = data[0] || {};
  const ts = parsePercentage(latestGame['True shooting percentage']);
  const ppp = getNum(latestGame["Points per player's possession"]);
  const usage = parsePercentage(latestGame['Usage Percentage']);
  const netRating = getNum(latestGame['Net rating']);
  const assistRatio = aggregatedStats.turnovers > 0 ? aggregatedStats.assists / aggregatedStats.turnovers : perGame.assists;
  const offRating = getNum(latestGame['Offensive rating']);
  const defRating = getNum(latestGame['Defensive rating']);
  const oppFg = parsePercentage(latestGame['Opponent\'s field goals, %']);

  return {
    position,
    dnaScores: {
      scoring: calculateScoringScore(latestGame, position),
      playmaking: calculatePlaymaking(latestGame, position),
      defense: calculateDefenseScore(latestGame, position),
      impact: calculateImpactScore(latestGame, position)
    },
    scoring: [
      { label: 'True Shooting %', value: ts * 100, isPercentage: true },
      { label: 'Points Per Possession', value: ppp, isPercentage: false }
    ],
    playmaking: [
      { label: 'Assist/Turnover', value: assistRatio, isPercentage: false },
      { label: 'Points Created', value: perGame.pointsCreated, isPercentage: false },
      { label: 'Assists Per Game', value: perGame.assists, isPercentage: false }
    ],
    defense: [
      { label: 'Defensive Rating', value: defRating, isPercentage: false },
      { label: 'Opponent FG%', value: oppFg * 100, isPercentage: true },
      { label: 'Blocks Per Game', value: perGame.blocks, isPercentage: false },
      { label: 'Steals Per Game', value: perGame.steals, isPercentage: false },
      { label: 'Contested Shots', value: perGame.contested, isPercentage: false }
    ],
    efficiency: [
      { label: 'Net Rating', value: netRating, isPercentage: false },
      { label: 'Offensive Rating', value: offRating, isPercentage: false },
      { label: 'Defensive Rating', value: defRating, isPercentage: false },
      { label: 'Usage Rate', value: usage * 100, isPercentage: true },
      { label: 'Fouls Drawn', value: aggregatedStats.foulsDrawn / aggregatedStats.games, isPercentage: false }
    ]
  };
};