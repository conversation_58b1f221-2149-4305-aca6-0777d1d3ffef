import { Metrics } from '../types/metrics';

type ScoreType = 'scoring' | 'playmaking' | 'defense' | 'impact';

const formatValue = (value: number | undefined, isPercentage: boolean = false): string => {
  if (value === undefined || isNaN(value)) return '-';
  return `${value.toFixed(1)}${isPercentage ? '%' : ''}`;
};

export const getScoreExplanation = (type: ScoreType, metrics: Metrics): string => {
  switch (type) {
    case 'scoring': {
      const ts = metrics.scoring.find(m => m.label === 'True Shooting %')?.value;
      const ppp = metrics.scoring.find(m => m.label === 'Points Per Possession')?.value;
      const usage = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value;
      
      return `True Shooting %: ${formatValue(ts, true)}
Points Per Possession: ${formatValue(ppp)}
Usage Rate: ${formatValue(usage, true)}`;
    }
    
    case 'playmaking': {
      const ratio = metrics.playmaking.find(m => m.label === 'Assist/Turnover')?.value;
      const created = metrics.playmaking.find(m => m.label === 'Points Created')?.value;
      const assists = metrics.playmaking.find(m => m.label === 'Assists Per Game')?.value;
      const usage = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value;
      
      return `Assist/Turnover Ratio: ${formatValue(ratio)}
Points Created: ${formatValue(created)}
Assists Per Game: ${formatValue(assists)}
Usage Rate: ${formatValue(usage, true)}`;
    }
    
    case 'defense': {
      const rating = metrics.defense.find(m => m.label === 'Defensive Rating')?.value;
      const oppFg = metrics.defense.find(m => m.label === 'Opponent FG%')?.value;
      const contested = metrics.defense.find(m => m.label === 'Contested Shots')?.value;
      const blocks = metrics.defense.find(m => m.label === 'Blocks Per Game')?.value;
      const steals = metrics.defense.find(m => m.label === 'Steals Per Game')?.value;
      
      return `Defensive Rating: ${formatValue(rating)}
Opponent FG%: ${formatValue(oppFg, true)}
Contested Shots: ${formatValue(contested)}
Blocks Per Game: ${formatValue(blocks)}
Steals Per Game: ${formatValue(steals)}`;
    }
    
    case 'impact': {
      const netRating = metrics.efficiency.find(m => m.label === 'Net Rating')?.value;
      const offRating = metrics.efficiency.find(m => m.label === 'Offensive Rating')?.value;
      const defRating = metrics.efficiency.find(m => m.label === 'Defensive Rating')?.value;
      const usage = metrics.efficiency.find(m => m.label === 'Usage Rate')?.value;
      const fouls = metrics.efficiency.find(m => m.label === 'Fouls Drawn')?.value;
      
      return `Net Rating: ${formatValue(netRating)}
Offensive Rating: ${formatValue(offRating)}
Defensive Rating: ${formatValue(defRating)}
Usage Rate: ${formatValue(usage, true)}
Fouls Drawn: ${formatValue(fouls)}`;
    }
    
    default:
      return 'No data available';
  }
};