import React from 'react';
import { Metric } from '../types/metrics';

interface MetricSectionProps {
  title: string;
  metrics: Metric[];
}

export const MetricSection: React.FC<MetricSectionProps> = ({ title, metrics }) => (
  <div className="bg-white/40 backdrop-blur-sm p-4 rounded-lg border border-primary/10 hover:border-primary/20 transition-all hover:shadow-md">
    <h3 className="text-lg font-semibold mb-3 text-transparent bg-gradient-text bg-clip-text">{title}</h3>
    <div className="space-y-2">
      {metrics.map(({ label, value, isPercentage }) => (
        <div key={label} className="flex justify-between">
          <span className="text-gray-600">{label}</span>
          <span className="font-medium text-gray-900">
            {isNaN(value) ? '-' : `${value.toFixed(1)}${isPercentage ? '%' : ''}`}
          </span>
        </div>
      ))}
    </div>
  </div>
);