import React from 'react';
import { Tooltip } from './Tooltip';

interface DNACircleProps {
  icon: React.ElementType;
  label: string;
  value: number;
  color: string;
  explanation: string;
}

export const DNACircle: React.FC<DNACircleProps> = ({ icon: Icon, label, value, color, explanation }) => {
  const [borderColor, textColor] = color.split(' ');
  
  return (
    <div className="relative flex flex-col items-center">
      <Tooltip content={explanation}>
        <div className={`w-24 h-24 rounded-full bg-white ${borderColor} border-4 flex items-center justify-center 
          transition-all duration-300 hover:scale-105 cursor-help`}>
          <div className="flex flex-col items-center">
            <Icon className={`w-6 h-6 mb-1 ${textColor}`} />
            <span className={`text-2xl font-bold ${textColor}`}>{Math.round(value) || 0}</span>
          </div>
        </div>
      </Tooltip>
      <span className="mt-2 text-sm text-gray-600">{label}</span>
    </div>
  );
};