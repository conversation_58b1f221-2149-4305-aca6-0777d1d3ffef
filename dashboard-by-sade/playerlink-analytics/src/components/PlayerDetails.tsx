import React from 'react';
import { Tag } from 'lucide-react';

interface PlayerDetailsProps {
  name: string;
  playerType: string[];
  strengths: string[];
  weaknesses: string[];
  keywords: string[];
}

export const PlayerDetails: React.FC<PlayerDetailsProps> = ({ 
  name, 
  playerType, 
  strengths, 
  weaknesses,
  keywords 
}) => (
  <div className="bg-white/40 backdrop-blur-sm p-6 rounded-lg border border-primary/10 hover:border-primary/20 transition-all hover:shadow-md">
    <h2 className="text-2xl font-bold text-transparent bg-gradient-text bg-clip-text mb-2">{name}</h2>
    
    {/* Player Keywords */}
    <div className="mb-4 text-gray-600 italic">
      {keywords.join(' • ')}
    </div>
    
    {/* Player Types */}
    <div className="flex flex-wrap gap-2 mb-6">
      {playerType.map((type) => (
        <span key={type} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary border border-primary/10">
          <Tag className="w-4 h-4 mr-1" />
          {type}
        </span>
      ))}
    </div>
    
    <div className="grid md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-lg font-semibold text-transparent bg-gradient-text bg-clip-text mb-3">Strengths</h3>
        <ul className="space-y-2">
          {strengths.map((strength) => (
            <li key={strength} className="flex items-start">
              <span className="inline-block w-2 h-2 mt-2 mr-2 bg-green-500 rounded-full"></span>
              <span className="text-gray-700">{strength}</span>
            </li>
          ))}
        </ul>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-transparent bg-gradient-text bg-clip-text mb-3">Areas for Development</h3>
        <ul className="space-y-2">
          {weaknesses.map((weakness) => (
            <li key={weakness} className="flex items-start">
              <span className="inline-block w-2 h-2 mt-2 mr-2 bg-yellow-500 rounded-full"></span>
              <span className="text-gray-700">{weakness}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </div>
);