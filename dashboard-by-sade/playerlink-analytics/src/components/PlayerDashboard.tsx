import React, { useState } from 'react';
import * as XLSX from 'xlsx';
import { Metrics, PlayerPosition } from '../types/metrics';
import { FileUpload } from './FileUpload';
import { PlayerDetails } from './PlayerDetails';
import { StatCircles } from './StatCircles';
import { MetricsGrid } from './MetricsGrid';
import { analyzePlayer } from '../utils/playerAnalysis';
import { calculateMetrics } from '../utils/metricsCalculator';

const PlayerDashboard = () => {
  const [metrics, setMetrics] = useState<Metrics | null>(null);
  const [playerName, setPlayerName] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, position: PlayerPosition) => {
    const file = event.target.files?.[0];
    if (!file) {
      setError('No file selected');
      return;
    }

    console.log('File selected:', file.name);
    setPlayerName(file.name.replace(/\.[^/.]+$/, ""));

    const reader = new FileReader();
    
    reader.onerror = () => {
      setError('Error reading file');
      console.error('FileReader error:', reader.error);
    };

    reader.onload = (e) => {
      try {
        console.log('File loaded, processing...');
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        console.log('Sheets in workbook:', workbook.SheetNames);
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        
        const jsonData = XLSX.utils.sheet_to_json(firstSheet);
        
        // Log the column names from the first row
        if (jsonData.length > 0) {
          console.log('Available columns:', Object.keys(jsonData[0]));
          console.log('First row data:', jsonData[0]);
        }

        if (jsonData.length > 0) {
          console.log('Calculating metrics for position:', position);
          const result = calculateMetrics(jsonData, position);
          console.log('Calculated metrics:', result);
          setMetrics(result);
          setError('');
        } else {
          setError('No data found in spreadsheet');
        }
      } catch (error) {
        console.error('Error processing file:', error);
        setError('Error processing file. Please ensure it\'s a valid Excel file with the correct format.');
      }
    };

    reader.readAsArrayBuffer(file);
  };

  return (
    <div className="bg-gradient-card backdrop-blur-md p-6 rounded-xl shadow-lg border border-primary/10">
      <div className="space-y-6">
        <FileUpload onUpload={handleFileUpload} />
        
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-600">
            {error}
          </div>
        )}
        
        {metrics && (
          <>
            {playerName && (
              <PlayerDetails
                name={playerName}
                {...analyzePlayer(metrics)}
              />
            )}
            <StatCircles metrics={metrics} />
            <MetricsGrid metrics={metrics} />
          </>
        )}
      </div>
    </div>
  );
};

export default PlayerDashboard;