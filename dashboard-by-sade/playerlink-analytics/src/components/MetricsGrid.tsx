import React from 'react';
import { MetricSection } from './MetricSection';
import { Metrics } from '../types/metrics';

interface MetricsGridProps {
  metrics: Metrics;
}

export const MetricsGrid: React.FC<MetricsGridProps> = ({ metrics }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <MetricSection title="Scoring Metrics" metrics={metrics.scoring} />
    <MetricSection title="Playmaking Metrics" metrics={metrics.playmaking} />
    <MetricSection title="Defense Metrics" metrics={metrics.defense} />
    <MetricSection title="Efficiency Metrics" metrics={metrics.efficiency} />
  </div>
);