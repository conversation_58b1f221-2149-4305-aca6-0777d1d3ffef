import React, { useState } from 'react';
import { PlayerPosition } from '../types/metrics';

interface FileUploadProps {
  onUpload: (event: React.ChangeEvent<HTMLInputElement>, position: PlayerPosition) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onUpload }) => {
  const [position, setPosition] = useState<PlayerPosition>('guard');

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <select 
          value={position}
          onChange={(e) => setPosition(e.target.value as PlayerPosition)}
          className="px-4 py-2 rounded-lg border border-primary/20 bg-white/80 text-gray-700"
        >
          <option value="guard">Guard/Primary Playmaker</option>
          <option value="wing">Wing/Secondary Playmaker</option>
          <option value="big">Big/Center</option>
        </select>
        
        <input
          type="file"
          accept=".xlsx,.xls"
          onChange={(e) => onUpload(e, position)}
          className="block w-full text-sm text-gray-600
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-full file:border-0
                    file:text-sm file:font-semibold
                    file:bg-gradient-to-r file:from-primary file:to-[#00b4ff]
                    file:text-white
                    hover:file:from-[#0080eb] hover:file:to-[#00a9f0]
                    cursor-pointer"
        />
      </div>
    </div>
  );
};