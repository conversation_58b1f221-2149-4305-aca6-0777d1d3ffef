import React from 'react';
import { Target, Brain, Shield, Zap } from 'lucide-react';
import { DNACircle } from './DNACircle';
import { Metrics } from '../types/metrics';
import { getScoreExplanation } from '../utils/scoreExplanations';

interface StatCirclesProps {
  metrics: Metrics;
}

export const StatCircles: React.FC<StatCirclesProps> = ({ metrics }) => (
  <div className="flex justify-between mb-8">
    <DNACircle 
      icon={Target} 
      label="Scoring" 
      value={metrics.dnaScores.scoring} 
      color="border-red-500 text-red-500"
      explanation={getScoreExplanation('scoring', metrics)}
    />
    <DNACircle 
      icon={Brain} 
      label="Playmaking" 
      value={metrics.dnaScores.playmaking} 
      color="border-green-500 text-green-500"
      explanation={getScoreExplanation('playmaking', metrics)}
    />
    <DNACircle 
      icon={Shield} 
      label="Defense" 
      value={metrics.dnaScores.defense} 
      color="border-yellow-500 text-yellow-500"
      explanation={getScoreExplanation('defense', metrics)}
    />
    <DNACircle 
      icon={Zap} 
      label="Impact" 
      value={metrics.dnaScores.impact} 
      color="border-purple-500 text-purple-500"
      explanation={getScoreExplanation('impact', metrics)}
    />
  </div>
);