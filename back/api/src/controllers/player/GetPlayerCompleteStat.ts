import { Request, Response } from "express";
import { Auth<PERSON><PERSON>, Controller, HttpMethod } from "../../helpers/Controller";
import {DBCollection, GenderCategory, PlayerPosition} from "@shared/enums";
import bim, {IError} from "@snark/bim";
import {
    addError,
    endErrors,
    startErrors,
    validateObjectId
} from "../../helpers/validation";
import {
	Defense,
	Efficiency,
	IPlayerDB,
	PlayerCompleteStat,
	PlayerOverview, PlayMaking,
	Scoring,
	SearchContext
} from "@shared/types";
import {DBState} from "@snark/crud";
import {searchPlayersBySimilarity} from "../../helpers/ai/vector/bigquery";
import {computeAge} from "@commons/helpers";
import moment from "moment-timezone";
import {playerVectorToMetrics} from "../../helpers/ai/commons";
import {ObjectId} from "@snark/mongodb-operator";

export default class GetPlayerCompleteStat extends Controller {
    // noinspection JSUnusedLocalSymbols
    private method = HttpMethod.get;
    // noinspection JSUnusedLocalSymbols
    private route = "/player/:playerId/complete-stat";
    // noinspection JSUnusedLocalSymbols
    private auth = AuthMode.authenticated;

    async validate(req: Request): Promise<IError | IError[] | null> {
        const errors = startErrors();
        addError(errors, validateObjectId(req.params, "playerId"));
        return endErrors(errors);
    }

    public async handler(req: Request, res: Response): Promise<any> {
        const playerId = db.toObjectID(req.params.playerId)!;

        const player: IPlayerDB = await db.findOne(DBCollection.player, { _id: playerId, state: {$ne: DBState.deleted} });
        if(!player) {
            return bim.notFound().id("notfound").context("playerId");
        }

        // --- similar players
        let similarPlayerIds : ObjectId[];
        if(!player.similarPlayers || player.similarPlayers.length === 0) {
            const ids = await getSimilarPlayers(player);
            similarPlayerIds = ids.map(id => db.toObjectID(id)!);

            await db.updateOne(DBCollection.player, {_id: playerId}, {
                $set: {
                    similarPlayers: similarPlayerIds
                }
            });
        }
        else {
            similarPlayerIds = player.similarPlayers as unknown as ObjectId[];
        }
        const similarPlayers = await db.find(DBCollection.player, {_id: {$in: similarPlayerIds}});

		const stats = await db.find(DBCollection.hudlPlayerSeasonStat, {id: player.external_id, seasonId: {$ne: null}, tournamentId: {$ne: null}}, {sort: {seasonId: -1}});

        const playerStats = <PlayerCompleteStat>{
            playerId: player._id.toString(),
            player,

			// FAKE
            badges: [], //getBadges(),
            insights: [], /*[
                {
                    type: "strengths",
                    value: ["Significant two-way impact through versatile play on both ends"]
                },
                {
                    type: "development",
                    value: [
                        "Can improve catch-and-shoot efficiency from three-point range",
                        "Should develop more consistent mid-range game off movement."
                    ]
                }
            ],*/
            recentForm: [], //getRecentForms(),
            upsidePotentialScore: {value: 0, avg: 0}, /* {
                value: Math.floor(Math.random() * 100),
                avg: Math.floor(Math.random() * 100)
            },*/

			// COMPUTED
			overview: getOverview(stats),
            scoring: getScoring(stats),
            efficiency: getEfficiency(stats),
            defense: getDefense(stats),
            playMaking: getPlayMaking(stats),
            similarPlayerIds: similarPlayers.map(player => player._id),
            similarPlayers
        }

        return playerStats;
    }
}

async function getSimilarPlayers(player: IPlayerDB) {
    const age = player.external_birthday ? computeAge(moment(player.external_birthday).valueOf()) : 30;

    const playerVector = await db.findOne(DBCollection.player_vector, {playerId: player._id});

    const context: SearchContext = {
        language: "en",
        minAge: age - 5,
        maxAge: age + 5,
        gender: getGenderCategory(player.external_gender_name),
        lastSeasonMinYear: player.lastSeasonYear,
        position: player.external_position1_name ? (player.external_position1_name as PlayerPosition): undefined,
        nationality: player.external_country1_name ? [player.external_country1_name] : undefined,
        metrics: playerVector ? playerVectorToMetrics(playerVector.vector) : undefined
    };

    const results = await searchPlayersBySimilarity(context, 5);
    return results.map(r => r.playerId).filter(id => id.toString() !== player._id.toString());
}

function getGenderCategory(gender: string): GenderCategory {
    if(gender === "F") {
        return GenderCategory.female;
    }
    else if(gender === "M") {
        return GenderCategory.male;
    }
    else {
        return GenderCategory.mixed;
    }
}

// ---------------- FAKE

function getBadges() {
    const categories = ["scoring", "playmaking", "defensive", "athletic", "specialist", "intangibles"];
    const PlayerTypes = {
        scoring: {
            pure: 'Pure Scorer',
            threeLevel: 'Three-Level Scorer',
            sharpshooter: 'Sharpshooter',
            slasher: 'Slasher',
            volumeShooter: 'Volume Shooter',
            clutch: 'Clutch Performer',
            postScorer: 'Post Scorer',
            isoSpecialist: 'Iso Specialist'
        },
        playmaking: {
            floorGeneral: 'Floor General',
            facilitator: 'Facilitator',
            pickAndRollMaestro: 'Pick-and-Roll Maestro',
            passFirst: 'Pass-First Guard',
            creativePlaymaker: 'Creative Playmaker',
            tempoSetter: 'Tempo Setter'
        },
        defensive: {
            lockdown: 'Lockdown Defender',
            rimProtector: 'Rim Protector',
            perimeterDefender: 'Perimeter Defender',
            defensiveAnchor: 'Defensive Anchor',
            stealSpecialist: 'Steal Specialist',
            reboundingMachine: 'Rebounding Machine'
        },
        athletic: {
            highFlyer: 'High-Flyer',
            speedster: 'Speedster',
            agileFinisher: 'Agile Finisher',
            powerPlayer: 'Power Player',
            enduranceBeast: 'Endurance Beast'
        },
        specialist: {
            threeAndD: '3-and-D Player',
            stretchBig: 'Stretch Big',
            hustler: 'Hustler',
            glueGuy: 'Glue Guy',
            sixthMan: 'Sixth Man'
        },
        intangibles: {
            teamLeader: 'Team Leader',
            mentor: 'Mentor',
            playoffPerformer: 'Playoff Performer',
            ironMan: 'Iron Man',
            wildcard: 'Wildcard'
        }
    }

    const cats = pickRandomItems(categories, 0, 4);
    return cats.map(cat => {
        const catTypes = Object.keys(PlayerTypes[cat]);
        const type = pickRandomItem(catTypes, 1);
        if(type) {
            const typeLabel = PlayerTypes[cat][type];
            return {
                label: typeLabel,
                description: `Badge description for ${typeLabel}...`,
                type: "standard"
            };
        }
        else {
            return null;
        }
    }).filter(b => b !== null);
}

function pickRandomItem<T>(array: T[], count: number): T|null {
    const results = pickRandomItems(array, count, count);
    if(results.length > 0) {
        return results[0];
    }
    else {
        return null;
    }
}

function pickRandomItems<T>(array: T[], min: number, max?: number): T[] {
    if(max === undefined) {
        max = min;
    }

    const count = Math.floor(Math.random() * (max - min + 1)) + min;
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}

function getRecentForms() {
    const labels = ["PG", "PG", "PG", "QF", "SF", "Final"]
    return labels.map(label => {
        return {
            label,
            wins: Math.floor(Math.random() * 25),
            losses: Math.floor(Math.random() * 25)
        };
    })
}

// ---------------- COMPUTED

function getOverview(stats: any[]): PlayerOverview[] {
    const seasons: any = {34: {name: "2024-2025"}, 32: {name: "2023-2024"}, 30: {name: "2022-2023"}, 28: {name: "2021-2022"}};
	for(const stat of stats) {
		if(seasons[stat.seasonId.toString()] !== undefined) {
			seasons[stat.seasonId.toString()].stats = stat;
		}
	}
	console.log("STATS = ", stats);

	const overview: PlayerOverview[] = [];
    for(const seasonId of Object.keys(seasons)) {
		const season = seasons[seasonId];
		console.log("SEASON: ", season);
		if(season.stats?.overview) {
			overview.push({
				seasonId,
				seasonName: season.name,
				data: season.stats.overview ?? []
			});
		}
    }
	return overview;
}

function getScoring(stats: any[]): Scoring[] {
	if(stats.length > 0) {
		return stats[0].scoring ?? [];
	}
	else {
		return [];
	}
}

function getEfficiency(stats: any[]): Efficiency[] {
	if (stats.length > 0) {
		return stats[0].efficiency ?? [];
	}
	else {
		return [];
	}
}

function getDefense(stats: any[]): Defense {
	if (stats.length > 0) {
		return stats[0].defense ?? {metrics: [], opponentFG: 0};
	}
	else {
		return {metrics: [], opponentFG: 0};
	}
}

function getPlayMaking(stats: any[]): PlayMaking {
	if (stats.length > 0) {
		return stats[0].playMaking ?? {metrics: []};
	}
	else {
		return {metrics: []};
	}
}
