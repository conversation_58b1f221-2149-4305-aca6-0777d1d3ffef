import {<PERSON>dl<PERSON><PERSON>} from "../helpers/hudl/HudlApi";
import {DBCollection, TournamentScope,} from "@shared/enums";
import {
	HudlAggregatedStatParam,
	HudlMatchLineupTeam, HudlPlayerStats,
	HudlSimplePlayer,
	HudlTournament
} from "../helpers/hudl/hudlTypes";
import {HudlMatchStatus} from "../helpers/hudl/hudlEnums";
import moment from "moment-timezone";

type PlayerWork = {
	player: HudlSimplePlayer;
	teamId: number;
	playerData?: {
		position: string
		hand: string
		gender: string
		height: number
		weight: number
	}
	param: HudlAggregatedStatParam[]
}

export type TournamentStat = {
	name: string
	min: number
	max: number
	count: number
	avg_sum: number
}


/**
 * From one tournament, update the aggregated stats for each player playing in this tournament.
 */
export default async function updateTournamentPlayersStats(tournamentId: number, seasonId: number): Promise<void> {
	let count: number;
	const start = moment.now();

	try {
		logger.debug("🤖 STARTING UPDATE TOURNAMENT PLAYERS STATS");
		const api = new HudlApi(false);

		logger.info("Loading tournament");
		const tournament: HudlTournament|null = await db.findOne(DBCollection.hudlTournament, {hudlId: tournamentId});
		if(!tournament) {
			logger.error(`Tournament ${tournamentId} not found`);
			process.exit(-2);
		}

		logger.info("Loading season");
		const season: HudlTournament|null = await db.findOne(DBCollection.hudlSeason, {hudlId: seasonId.toString()});
		if(!season) {
			logger.error(`Season ${seasonId} not found`);
			process.exit(-3);
		}

		logger.info("Loading matches from tournament");
		const matches = await api.getTournamentMatches(tournamentId, seasonId);
		const totalMatches = matches.length;
		logger.info(`   Found ${matches.length} matches.`);

		let playersToUpdate: Record<string, PlayerWork> = {};
		logger.info("Parse matches lineups");
		count = 0;
		for(const match of matches) {
			if (match.status_id !== HudlMatchStatus.Scheduled) {
				logger.debug("   Match: " + match.match_name);
				// --- get match lineups
				const lineUps = await api.getMatchLineUps(match.id);
				if (lineUpWellFormatted(lineUps.first_team)) {
					addPlayers(playersToUpdate, getPlayersFromLineUps(lineUps.first_team), parseInt(match.team1_id));
				}
				if (lineUpWellFormatted(lineUps.second_team)) {
					addPlayers(playersToUpdate, getPlayersFromLineUps(lineUps.second_team), parseInt(match.team2_id));
				}
			}

			if(++count % 10 === 0) {
				logger.debug(`${Math.round(count/totalMatches*100)}% ------ Matches lineups treated: ${count}/${totalMatches}`);
			}
		}
		logger.debug(`${totalMatches} matches lineups treated.`);

		logger.info(`Players to update: ${Object.keys(playersToUpdate).length}`);

		logger.info("Get players aggregated stats");
		const totalPlayers = Object.keys(playersToUpdate).length;
		count = 0;
		let tournamentStats: Record<string, TournamentStat> = {};

		for(const pt of Object.values(playersToUpdate)) {
			logger.debug(`   Player: ${pt.player.id} - ${pt.player.firstname} ${pt.player.lastname}`);
			const player = await api.getPlayerInfo(pt.player.id);
			let playerData: any = {};
			if(player) {
				pt.playerData = {
					position: player.position1_name,
					hand: player.hand_name,
					gender: player.gender_name,
					height: player.height,
					weight: player.weight
				};
			}
			pt.param = await api.getPlayerAggregatedStats(pt.player.id, seasonId);
			for(const p of pt.param) {
				if(tournamentStats[p.name] === undefined) {
					tournamentStats[p.name] = {
						name: p.name,
						min: p.value_avg,
						max: p.value_avg,
						count: 1,
						avg_sum: p.value_avg
					};
				}
				else {
					tournamentStats[p.name].min = Math.min(tournamentStats[p.name].min, p.value_avg);
					tournamentStats[p.name].max = Math.max(tournamentStats[p.name].max, p.value_avg);
					tournamentStats[p.name].count++;
					tournamentStats[p.name].avg_sum += p.value_avg;
				}
			}
			// await db.updateOne(DBCollection.hudlPlayerSeasonStat, {
			// 	id: parseInt(pt.player.id),
			// 	seasonId
			// }, {
			// 	$set: {
			// 		id: parseInt(pt.player.id),
			// 		tournamentId,
			// 		tournamentName: tournament.name,
			// 		seasonId,
			// 		season: season.name,
			// 		firstname: pt.player.firstname,
			// 		lastname: pt.player.lastname,
			// 		...playerData,
			// 		param: playerStats
			// 	},
			// 	$addToSet: {
			// 		teams: pt.teamId
			// 	}
			// }, {upsert: true});

			if(++count % 10 === 0) {
				logger.debug(`${Math.round(count/totalPlayers*100)}% ------ Players treated: ${count}/${totalPlayers}`);
			}
		}
		logger.info("Players fetched.");
		logger.info("Data Normalization...");



		const end = moment.now();
		const duration = moment.duration(end - start);
		logger.info(`Total time: ${duration.humanize()}`);

		logger.debug("✅  END OF UPDATE TOURNAMENT PLAYERS STATS");
	}
	catch(err) {
		logger.error("❌ ERROR ON UPDATE TOURNAMENT PLAYERS STATS");
		logger.error(err);
	}
}

function lineUpWellFormatted(lineUps: HudlMatchLineupTeam[]): boolean {
	return lineUps.length > 0 && lineUps[0].lineup.length > 0 && lineUps[0].lineup[0].main.length > 0 && !!lineUps[0].lineup[0].main[0].player;
}

function getPlayersFromLineUps(lineUps: HudlMatchLineupTeam[]): HudlSimplePlayer[] {
	return lineUps[0].lineup[0].main[0].player;
}

function addPlayers(playersToUpdate: Record<string, PlayerWork>, players: HudlSimplePlayer[], teamId: number) {
	for(const player of players) {
		playersToUpdate[player.id] = {player, teamId, param: []};
	}
}