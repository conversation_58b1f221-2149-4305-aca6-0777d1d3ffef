#!/bin/zsh

# build could be local or cloud or none (none = no build at all)
BUILD=local

# --- config
PROJECT_ID=snappy-cosine-439512-i3
SERVICE_NAME=playerlynk-api
DOCKER_FILE=api.dockerfile
DOCKER_CONTEXT=.
REGION=europe-west1
VPC_CONNECTOR=cloud-run-connector-west1

REPOSITORY_HOST=${REGION}-docker.pkg.dev
IMAGE_NAME=${REPOSITORY_HOST}/${PROJECT_ID}/cloud-run-source-deploy/${SERVICE_NAME}
DATE=$(date +%Y_%m_%d_%H_%M_%S)

# --- build
if [ "$BUILD" != "none" ]; then
    pushd player-lynk > /dev/null || exit
    BUILD_HASH=$(git rev-parse --short HEAD)-${DATE}
    popd > /dev/null || exit

    if [ "$BUILD" = "local" ]; then
        echo --------------------------------------------------------------------------------------
        echo BUILDING ${SERVICE_NAME} DOCKER IMAGE LOCALLY...
        echo
        docker buildx build -f ${DOCKER_FILE} \
            --platform linux/amd64 \
            -t ${IMAGE_NAME}:latest \
            -t ${IMAGE_NAME}:"${BUILD_HASH}" \
            ${DOCKER_CONTEXT}
        gcloud auth configure-docker ${REPOSITORY_HOST}
        docker push --all-tags ${IMAGE_NAME}
    else
        echo --------------------------------------------------------------------------------------
        echo BUILDING ${SERVICE_NAME} DOCKER IMAGE ON GCLOUD...
        echo
        gcloud builds submit --tag ${IMAGE_NAME} --platform linux/amd64 --tag latest --skip-tests=true
    fi
fi
# --- deploy
echo --------------------------------------------------------------------------------------
echo DEPLOYING ${SERVICE_NAME} TO CLOUD...
echo
gcloud run deploy ${SERVICE_NAME} --image ${IMAGE_NAME} --project ${PROJECT_ID} --region ${REGION} \
    --platform managed \
    --memory 256Mi \
    --timeout 300 \
    --concurrency 50 \
    --max-instances 1 \
    --cpu 1 \
    --startup-probe tcpSocket.port=8080,initialDelaySeconds=2,periodSeconds=10,timeoutSeconds=4 \
    --set-env-vars NODE_ENV=production \
    --clear-cloudsql-instances \
    --allow-unauthenticated \
    --vpc-connector ${VPC_CONNECTOR}
