
## Services

| Service  | Port  | Staging URL                              | Prod URL                  |
|----------|-------|------------------------------------------|---------------------------|
| Front    | 28100 | https://playerlynk.startomatic.space     | https://app.playerlynk.io |
| API      | 28101 | https://api.playerlynk.startomatic.space | https://api.playerlynk.io |
| BO       | 28102 | https://bo.playerlynk.startomatic.space  | https://bo.playerlynk.io  |
| Geocoder | 28103 | -                                        | -                         |
| Notifier | 28104 | -                                        | -                         |

## Launch services

### Quick start
After a git pull.
 
- Install globally `pm2` and `zx` libs.
- put your environment string in /bin/.env file. Ex: `echo staging > .env`
- Run `./bin/restart.js all -y`

### Services description
Be carefull, to install manually @snark libs (shared libs of our own) you have to copy `.npmrc.prod` file to `.npmrc` 
in each directory (The `bin/restart.js` script does it when running). 

#### Back
All services are NodeJS services.

The configuration of each services are in `/config` directory, in JSON5 files. 

package.json of all services contains scripts to run the service.
- `dev` to run the service in dev mode.
- `build` to build the service.
- `build-watch` to build and watch for changes.

There are 5 services in the back directory (3 are used at this time).
- commons 
  - some global helpers shared by all back services
  - this service should be installed first to be able to build other services.
- api
    - the main API for all other services
- notifier
  - this service is responsible of sending notifications to users by email, SMS, Push
- hudl-bridge
  - Service to get last data from Hudl. For now it's deactivated and you should not run it (The Hudl account is deactivated at this moment)
- geocoder
  - A global service to geocode addresses and reverse geocode locations (Not used at this time by the project)

#### Backoffice
VueJS web application

Configuration file are in .env.* files

- bo
  - Be careful if you run it, you should use NodeJS version 16.
  - It's a quick Backoffice for all data from MongoDB database. For now it has not been optimized and it should disappear in the future to have a better backoffice management system.

To init a user for the backoffice, you should run the script `/back/api/scripts/initData.js` it create a backoffice user with credentials `backoffice/backoffice`

#### Front 
VueJS web application

Configuration file are in .env.* files

- front
  - It's the main interface for users of the project.

#### shared
This directory is used by all others services and contains shared types and enumerations.

### Databases

There are 2 databases for the project.

The main one is a MongoDB Database.

The other one is a Chroma Vector Database for similarity search between players (used by our IA features in progress)

The chroma and mongodb server are autonomous and managed on the server. 

The databases dump could not be integrated in the git repository (Chroma dump is more than 1.2Go and The MongoDB database is about 700Mo)

### R&D and Hudl works
You could find in `hudl` and `r-d` directories projects used respectively to work with external hudl services (to retrieve sport data) and to work on AI features of the project.

#### hudl
- hudl-save
  - exploration of hudl data
- data-server
  - API to interface hudl services (no-longer used for now)

#### r-d
- playerlynk-ai-chat-test
  - some test for the first AI experience in project with OpenAI LLM
- vector-search (in python)
  - Initialization of the chroma vector database for similarity search between players



#### TODO:
Mettre le copyright des icones et illustrations : 

- Pour l'icone de personne qui parle.
   - speak by Larea from <a href="https://thenounproject.com/browse/icons/term/speak/" target="_blank" title="speak Icons">Noun Project</a> (CC BY 3.0)

# Google Cloud
En local, penser à se réauthentifier.
```
gcloud auth application-default login 
```
