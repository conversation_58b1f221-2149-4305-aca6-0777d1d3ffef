/**
 * Initialisation des enregistrements pour indiquer quels tournois et saisons
 * doivent être recalculé complètement au niveau des stats de joueurs.
 *
 * Ce script n'aura certainement plus de raison d'être une fois que la base de données sera totalement initialisée
 * sur tous les tournois et toutes les saisons.
 */

import {connectToDatabase, createHudlApi} from "@playerlynk/commons";
import {
	DBCollection,
	GenderCategory,
	IDirtyTournament,
	IOrganization,
	OrganizationType,
	Role,
	UserStatus
} from "@playerlynk/shared";
import moment from "moment-timezone";
import {ObjectId} from "@snark/mongodb-operator";
import bcrypt from "bcrypt";

const seasonLists = [34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 0];

const priorityTournaments = [
	6, 156,	// College1 (NCAA Division)
];

(async () => {
	try {
		console.log("Start init Dirty Tournaments");

		const db = await connectToDatabase(true);
		const api = await createHudlApi(db);

		const allTournaments = await db.find(DBCollection.tournament, {});
		const dirtyTournmanents: IDirtyTournament[] = [];

		for(const seasonId of seasonLists) {

		}


		console.log("End init Dirty Tounraments");
		process.exit(0);
	}
	catch (err: any) {
		console.error("❌ ERROR ON INIT LOUISVILLE");
		console.error(err);
		process.exit(-1)
	}
})();
