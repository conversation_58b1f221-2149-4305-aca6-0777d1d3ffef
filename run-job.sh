#!/bin/zsh
set -euo pipefail

REGION=europe-west1

if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <update-seasons|update-tournaments|tournament-player-stats|tournament-dirty-manager> <command args?>"
    exit 1
fi
if [[ $1 == "update-seasons" ]]; then
    JOB_NAME=playerlynk-update-seasons
elif [[ $1 == "update-tournaments" ]]; then
    JOB_NAME=playerlynk-update-tournaments
elif [[ $1 == "tournament-player-stats" ]]; then
    JOB_NAME=playerlynk-tournament-player-stats
elif [[ $1 == "tournament-dirty-manager" ]]; then
    JOB_NAME=playerlynk-tournament-dirty-manager
else
    echo "Unknown job: $1"
    exit 1
fi

if [[ $# -gt 1 ]]; then
    shift
    IFS=','
    joinedArgs="$*"
    unset IFS
    gcloud run jobs execute ${JOB_NAME} --region=${REGION} --args="dist/index.js,$joinedArgs"
else
    gcloud run jobs execute ${JO<PERSON>_NAME} --region=${REGION}
fi
